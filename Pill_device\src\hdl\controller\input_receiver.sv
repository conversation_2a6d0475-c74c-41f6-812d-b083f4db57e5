/*
 * 输入接收器模块
 *
 * 功能描述：
 * - 检测键盘、按钮和药片传感器的输入信号
 * - 实现边沿检测，将电平信号转换为脉冲信号
 * - 防止按键抖动和重复触发
 *
 * 输入信号处理：
 * - 键盘输入：16位矩阵键盘，检测按键按下的瞬间
 * - 按钮输入：5个功能按钮，检测按下的瞬间
 * - 药片传感器：检测药片通过传感器的瞬间
 *
 * 边沿检测原理：
 * - 保存上一时钟周期的输入状态
 * - 当前状态为高且上一状态为低时，产生一个时钟周期的脉冲
 *
 * 作者：数字逻辑课程设计小组
 * 日期：2024-2025学年
 */

`include "config.svh"

module input_receiver (
    input logic clock,                  // 系统时钟
    input logic reset_n,                // 复位信号 (低电平有效)
    input logic [15:0] keyboard,        // 16位键盘输入信号
    input logic [4:0] button,           // 5位按钮输入信号
    input logic pill,                   // 药片传感器输入信号
    output logic [15:0] keyboard_down,  // 键盘按下脉冲输出
    output logic [4:0] button_down,     // 按钮按下脉冲输出
    output logic pill_pulse             // 药片检测脉冲输出
);

    // ========================================
    // 键盘输入边沿检测
    // ========================================

    /*
     * 键盘输入处理逻辑
     * - 检测16位键盘矩阵的按键按下事件
     * - 实现上升沿检测：从低电平到高电平的转换
     * - 输出一个时钟周期的脉冲信号
     */
    logic [15:0] last_keyboard;         // 保存上一时钟周期的键盘状态
    always @(posedge clock or negedge reset_n) begin
        if (~ reset_n) begin
            // 复位时清除所有状态
            keyboard_down <= 'b0;
            last_keyboard <= 'b0;
        end else begin
            // 边沿检测：当前为高且上一周期为低时产生脉冲
            keyboard_down = (~ last_keyboard) & keyboard;
            last_keyboard = keyboard;
        end
    end

    // ========================================
    // 按钮输入边沿检测
    // ========================================

    /*
     * 按钮输入处理逻辑
     * - 检测5个功能按钮的按下事件
     * - 按钮功能分配：
     *   button[0]: 减少/向左移动
     *   button[1]: 增加/向右移动
     *   button[2]: 向上移动
     *   button[3]: 状态切换/确认
     *   button[4]: 向下移动
     */
    logic [4:0] last_button;            // 保存上一时钟周期的按钮状态
    always @(posedge clock or negedge reset_n) begin
        if (~ reset_n) begin
            // 复位时清除所有状态
            button_down <= 'b0;
            last_button <= 'b0;
        end else begin
            // 边沿检测：当前为高且上一周期为低时产生脉冲
            button_down = (~ last_button) & button;
            last_button = button;
        end
    end

    // ========================================
    // 药片传感器边沿检测
    // ========================================

    /*
     * 药片传感器处理逻辑
     * - 检测药片通过传感器的瞬间
     * - 用于计数和验证药片装瓶过程
     * - 防止重复计数同一颗药片
     */
    logic last_pill;                    // 保存上一时钟周期的传感器状态
    always @(posedge clock or negedge reset_n) begin
        if (~ reset_n) begin
            // 复位时清除所有状态
            pill_pulse <= 'b0;
            last_pill <= 'b0;
        end else begin
            // 边沿检测：当前为高且上一周期为低时产生脉冲
            pill_pulse = (~ last_pill) & pill;
            last_pill = pill;
        end
    end

endmodule
