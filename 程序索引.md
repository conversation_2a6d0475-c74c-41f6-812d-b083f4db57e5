# C++面向对象程序设计 - 程序索引

**作者**: 学生  
**日期**: 2024-2025学年春季学期

---

## 📁 文件列表

| 序号 | 文件名 | 程序类型 | 主要功能 | 难度等级 | 修复状态 |
|------|--------|----------|----------|----------|----------|
| 1 | `1.cpp` | 基础练习 | 动态二维数组矩阵运算 | ⭐⭐ | ✅ 已修复内存管理问题 |
| 2 | `2_1.cpp` | 类设计 | 点类和圆类，圆的相交判断 | ⭐⭐ | ✅ 无问题 |
| 3 | `2_2.cpp` | 高级类设计 | 矩阵类与运算符重载 | ⭐⭐⭐ | ✅ 无问题 |
| 4 | `3.cpp` | 继承基础 | Shape继承体系，面积计算 | ⭐⭐⭐ | ✅ 已修复虚函数问题 |
| 5 | `4.cpp` | 应用程序 | 猜价格游戏 | ⭐ | ✅ 无问题 |
| 6 | `5_1.cpp` | 多态进阶 | 虚函数与多态的正确实现 | ⭐⭐⭐⭐ | ✅ 无问题 |
| 7 | `5-2.cpp` | 运算符重载 | Point类的++和--运算符 | ⭐⭐⭐ | ✅ 已修复运算符重载问题 |

---

## 🎯 按知识点分类

### 1. 内存管理
- **`1.cpp`**: 动态内存分配与释放
  - 二维指针的使用
  - 内存泄漏问题分析
  - 正确的内存释放方式

### 2. 类的基本设计
- **`2_1.cpp`**: Point和Cycle类
  - 构造函数和析构函数
  - 成员函数设计
  - 对象组合模式
  - const成员函数

### 3. 高级类特性
- **`2_2.cpp`**: Matrix类
  - 深拷贝构造函数
  - 赋值运算符重载
  - 异常处理
  - 三法则的应用

### 4. 继承与多态
- **`3.cpp`**: 基础继承
  - 公有继承
  - 构造析构顺序
  - 函数重写
  
- **`5_1.cpp`**: 高级多态
  - 纯虚函数
  - 虚析构函数
  - override关键字
  - 抽象基类

### 5. 运算符重载
- **`2_2.cpp`**: 算术运算符 (+, -, =)
- **`5-2.cpp`**: 自增自减运算符 (++, --)
  - 前置vs后置运算符
  - 临时对象的创建
  - 返回值类型选择

### 6. 应用程序设计
- **`4.cpp`**: 游戏程序
  - 随机数生成
  - 循环控制
  - 用户交互
  - 输入验证

---

## 🔍 按难度等级学习路径

### 入门级 (⭐)
1. **`4.cpp`** - 猜价格游戏
   - 熟悉基本语法
   - 理解程序流程控制

### 基础级 (⭐⭐)
2. **`1.cpp`** - 动态内存管理
   - 学习指针和动态分配
   - 理解内存管理重要性

3. **`2_1.cpp`** - 类的基本使用
   - 掌握类的定义和使用
   - 理解封装概念

### 中级 (⭐⭐⭐)
4. **`2_2.cpp`** - 深拷贝和运算符重载
   - 掌握深拷贝技术
   - 学习运算符重载

5. **`3.cpp`** - 继承基础
   - 理解继承关系
   - 掌握构造析构顺序

6. **`5-2.cpp`** - 运算符重载进阶
   - 区分前置后置运算符
   - 理解临时对象概念

### 高级 (⭐⭐⭐⭐)
7. **`5_1.cpp`** - 虚函数与多态
   - 掌握多态机制
   - 理解虚函数表
   - 学习现代C++特性

---

## 📚 学习建议

### 第一阶段：基础概念 (1-2周)
**学习顺序**: `4.cpp` → `2_1.cpp` → `1.cpp`

**重点掌握**:
- 类的定义和使用
- 构造函数和析构函数
- 动态内存分配

**练习建议**:
- 修改游戏规则，增加难度等级
- 为Point类添加更多功能
- 尝试修复1.cpp中的内存泄漏

### 第二阶段：高级特性 (2-3周)
**学习顺序**: `2_2.cpp` → `3.cpp` → `5-2.cpp`

**重点掌握**:
- 深拷贝vs浅拷贝
- 运算符重载规则
- 继承关系设计

**练习建议**:
- 为Matrix类添加乘法运算
- 扩展Shape继承体系
- 实现其他运算符重载

### 第三阶段：多态与设计 (2-3周)
**学习顺序**: `5_1.cpp` → 综合项目

**重点掌握**:
- 虚函数机制
- 多态的应用
- 设计模式思想

**练习建议**:
- 设计一个图形绘制系统
- 实现工厂模式创建图形
- 添加更多图形类型

---

## 🛠️ 编译和运行指南

### 编译命令
```bash
# 基本编译
g++ -o program_name source_file.cpp

# 带调试信息
g++ -g -o program_name source_file.cpp

# 启用所有警告
g++ -Wall -Wextra -o program_name source_file.cpp

# C++11标准
g++ -std=c++11 -o program_name source_file.cpp
```

### 运行和测试
```bash
# 运行程序
./program_name

# 使用输入重定向测试
./program_name < test_input.txt

# 内存检查 (Linux)
valgrind --leak-check=full ./program_name
```

---

## 🐛 常见问题快速索引

| 问题类型 | 相关文件 | 修复状态 | 解决方案位置 |
|----------|----------|----------|-------------|
| 内存泄漏 | `1.cpp` | ✅ 已修复 | 问题修复报告 + 学习笔记 |
| 浅拷贝问题 | `2_2.cpp` | ✅ 无问题 | 学习笔记 - 常见错误2 |
| 虚函数错误 | `3.cpp` | ✅ 已修复 | 问题修复报告 + 学习笔记 |
| 运算符重载 | `5-2.cpp` | ✅ 已修复 | 问题修复报告 + 学习笔记 |
| 构造顺序 | `3.cpp`, `5_1.cpp` | ✅ 无问题 | 程序注释中的说明 |

### 🔧 修复文档
- **`问题修复报告.md`** - 详细的修复过程和方案对比
- **`C++面向对象程序设计学习笔记.md`** - 更新了修复后的最佳实践
- **原程序文件** - 保留错误代码注释，添加修复版本

---

## 📈 学习进度跟踪

### 完成情况记录
- [ ] `4.cpp` - 猜价格游戏 ✅ 已完成
- [ ] `2_1.cpp` - 点类和圆类 ✅ 已完成  
- [ ] `1.cpp` - 动态数组 ✅ 已完成
- [ ] `2_2.cpp` - 矩阵类 ✅ 已完成
- [ ] `3.cpp` - 继承基础 ✅ 已完成
- [ ] `5-2.cpp` - 运算符重载 ✅ 已完成
- [ ] `5_1.cpp` - 虚函数多态 ✅ 已完成

### 扩展练习
- [ ] 修复1.cpp的内存管理问题
- [ ] 为Matrix类添加乘法运算
- [ ] 扩展Shape体系添加三角形类
- [ ] 实现字符串类的运算符重载
- [ ] 设计一个简单的图形编辑器

---

## 🎯 总结

这7个程序涵盖了C++面向对象编程的核心概念：

1. **基础语法**: 变量、函数、控制结构
2. **类与对象**: 封装、构造析构、成员函数
3. **内存管理**: 动态分配、指针、RAII
4. **继承**: 代码复用、层次设计
5. **多态**: 虚函数、动态绑定
6. **运算符重载**: 自然语法、类型扩展

通过系统学习这些程序，可以建立扎实的C++面向对象编程基础，为后续学习STL、模板、现代C++特性做好准备。

---

*建议按照推荐的学习路径逐步深入，每个阶段都要动手实践和扩展练习。*
