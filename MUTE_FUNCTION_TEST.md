# 静音功能测试指南

## 功能概述

本文档描述了药片装瓶系统中新增的静音功能的测试方法和验证步骤。

## 静音功能特性

### 1. 激活方式
- **操作方法**：同时按下按钮0和按钮1
- **信号类型**：边沿触发，检测按钮组合的上升沿
- **状态切换**：每次操作切换一次静音状态（开/关）

### 2. 状态保持
- **持续性**：静音状态会一直保持，直到再次切换
- **复位行为**：系统复位时自动取消静音状态
- **优先级**：静音具有最高优先级，可覆盖所有音频输出

### 3. 功能保护
- **指针控制**：静音切换时不影响水平指针移动
- **速度控制**：静音切换时不影响速度设置
- **状态切换**：按钮3的状态切换功能完全保留

## 测试步骤

### 测试1：基本静音功能
1. **初始状态检查**
   - 上电后系统应处于非静音状态
   - 键盘输入错误时应有警告音

2. **激活静音**
   - 同时按下按钮0和按钮1
   - 验证所有音频输出停止

3. **取消静音**
   - 再次同时按下按钮0和按钮1
   - 验证音频输出恢复正常

### 测试2：功能冲突检查
1. **水平指针测试**
   - 在非静音状态下，单独按下按钮0或按钮1，验证指针正常移动
   - 同时按下按钮0和按钮1时，验证指针不移动（优先处理静音）

2. **速度控制测试**
   - 在速度设置页面，单独按下按钮0或按钮1，验证速度正常调节
   - 同时按下按钮0和按钮1时，验证速度不变（优先处理静音）

3. **状态切换测试**
   - 验证按钮3的状态切换功能完全正常
   - 在各种状态下测试静音功能都能正常工作

### 测试3：音频优先级测试
1. **警告音测试**
   - 非静音状态：输入无效键盘组合，应有警告音
   - 静音状态：输入无效键盘组合，应无声音

2. **错误音测试**
   - 非静音状态：触发错误状态，应有错误音
   - 静音状态：触发错误状态，应无声音

3. **完成音乐测试**
   - 非静音状态：装瓶完成，应播放音乐
   - 静音状态：装瓶完成，应无声音

### 测试4：边界条件测试
1. **快速切换测试**
   - 快速多次按下按钮组合，验证状态切换正确
   - 验证不会出现状态混乱

2. **复位测试**
   - 在静音状态下复位系统
   - 验证复位后自动取消静音

3. **长时间保持测试**
   - 设置静音状态后长时间运行
   - 验证静音状态稳定保持

## 预期结果

### 正常工作指标
- ✅ 静音切换响应及时（1个时钟周期内）
- ✅ 静音状态稳定保持
- ✅ 不影响其他按钮功能
- ✅ 音频优先级正确
- ✅ 复位行为正确

### 故障排除

#### 问题1：静音无效果
- **可能原因**：按钮时序不同步
- **解决方法**：确保同时按下按钮0和按钮1

#### 问题2：指针移动异常
- **可能原因**：信号冲突
- **解决方法**：检查按钮组合检测逻辑

#### 问题3：状态切换异常
- **可能原因**：时钟域问题
- **解决方法**：检查时钟分配和同步

## 技术实现要点

### 1. 信号处理
```systemverilog
// 静音切换信号检测
assign mute_toggle_signal = button_down[0] && button_down[1];

// 边沿检测防止重复触发
if (mute_toggle_signal && ~last_mute_toggle) begin
    mute_state <= ~mute_state;
end
```

### 2. 功能保护
```systemverilog
// 水平指针控制保护
assign h_ptr_add = button_down[1] && ~mute_toggle_signal;
assign h_ptr_sub = button_down[0] && ~mute_toggle_signal;

// 速度控制保护
assign speed_up_signal = button_down[1] && (v_ptr == 2) && 
                        (state == setting_state) && ~mute_toggle_signal;
```

### 3. 音频控制
```systemverilog
// 使用静音状态寄存器而非按钮脉冲
audio_output_generator aog_ins (
    .no_response(mute_state),  // 持续的静音控制信号
    // ...
);
```

## 验证清单

- [ ] 基本静音功能正常
- [ ] 状态保持稳定
- [ ] 不影响指针控制
- [ ] 不影响速度控制
- [ ] 不影响状态切换
- [ ] 音频优先级正确
- [ ] 复位行为正确
- [ ] 边界条件处理正确

## 注意事项

1. **按钮时序**：确保按钮0和按钮1同时按下
2. **功能优先级**：静音切换优先于其他按钮功能
3. **状态一致性**：静音状态与系统其他状态独立
4. **测试环境**：在实际硬件上验证最终效果
