/*
 * 音频输出生成器模块
 *
 * 功能描述：
 * - 根据系统状态生成不同类型的音频信号
 * - 支持音乐播放、错误提示音、警告提示音
 * - 管理音频播放的时序和频率选择
 *
 * 音频类型：
 * - 音乐：装瓶完成时播放的旋律
 * - 错误音：系统错误时的蜂鸣声
 * - 警告音：输入错误时的提示音
 * - 静音：无响应状态或正常工作状态
 *
 * 优先级：no_response > music > error > warning
 *
 * 作者：数字逻辑课程设计小组
 * 日期：2024-2025学年
 */

`include "config.svh"

module audio_output_generator (
    input logic clock,              // 系统时钟
    input logic reset_n,            // 复位信号 (低电平有效)
    input logic no_response,        // 静音控制信号 (来自按钮3)
    input logic music,              // 音乐播放请求信号
    input logic error,              // 错误提示音请求信号
    input logic warning,            // 警告提示音请求信号
    output logic [31:0] frequency_select  // 频率选择输出 (控制音频频率)
);

    // ========================================
    // 内部信号定义
    // ========================================

    // 音乐播放控制信号
    logic music_working;                // 音乐播放状态标志
    logic [31:0] music_address;         // 音乐数据地址指针

    // 音乐数据数组 - 存储旋律的频率值
    // 数组包含32个音符，每个值代表一个音符的频率编码
    logic [31:0] music_data[0:31] = {
        9,   // 音符1
        12,  // 音符2
        16,  // 音符3
        20,  // 音符4
        16,  // 音符5
        16,  // 音符6
        16,  // 音符7
        15,  // 音符8
        16,  // 音符9
        16,  // 音符10
        16,  // 音符11
        15,  // 音符12
        16,  // 音符13
        16,  // 音符14
        9,   // 音符15
        11,  // 音符16
        12,  // 音符17
        16,  // 音符18
        15,  // 音符19
        12,  // 音符20
        9,   // 音符21
        9,   // 音符22
        8,   // 音符23
        8,   // 音符24
        4,   // 音符25
        4,   // 音符26
        4,   // 音符27
        4,   // 音符28
        4,   // 音符29
        4,   // 音符30
        4,   // 音符31
        4    // 音符32
    };

    // 错误提示音控制信号
    logic error_working;                // 错误音播放状态标志
    logic [31:0] error_address;         // 错误音数据地址指针
    logic [31:0] error_beep_data[0:1] = {35, 0};  // 错误音数据：{蜂鸣音, 静音}

    // 警告提示音控制信号
    logic warning_working;              // 警告音播放状态标志
    logic [31:0] warning_address;       // 警告音数据地址指针
    logic [31:0] warning_beep_data[0:1] = {35, 0}; // 警告音数据：{蜂鸣音, 静音}

    // 时序控制参数
    // 计算音符播放时长：基于系统频率和音频播放速度
    localparam ticking = 100000000 / (4 * `audio_bps);  // 每个音符的时钟周期数
    logic [$clog2(ticking)-1:0] ms;                     // 时序计数器

    // ========================================
    // 音频播放控制逻辑
    // ========================================

    /*
     * 主要功能：
     * 1. 根据输入信号的优先级启动相应的音频播放
     * 2. 管理音频播放的时序和地址指针
     * 3. 控制音频播放的开始、进行和结束
     */
    always @(posedge clock or negedge reset_n) begin
        if (~reset_n) begin
            // 复位时清除所有状态
            ms = 0;
            music_working = 0;
            music_address = 0;
            error_working = 0;
            error_address = 0;
            warning_working = 0;
            warning_address = 0;
        end else begin
            // 根据输入信号优先级选择音频类型
            casex ({
                no_response, music, error, warning
            })
                // 优先级1：静音模式 (no_response = 1)
                4'b1xxx: begin
                    ms = 0;
                    music_working = 0;
                    music_address = 0;
                    error_working = 0;
                    error_address = 0;
                    warning_working = 0;
                    warning_address = 0;
                end
                // 优先级2：音乐播放模式 (music = 1, no_response = 0)
                4'b01xx: begin
                    if (~music_working) begin
                        // 启动音乐播放
                        ms = 0;
                        music_working = 1;
                        music_address = 0;
                        error_working = 0;
                        error_address = 0;
                        warning_working = 0;
                        warning_address = 0;
                    end
                end
                // 优先级3：错误提示音模式 (error = 1, music = 0, no_response = 0)
                4'b001x: begin
                    if (~error_working) begin
                        // 启动错误提示音播放
                        ms = 0;
                        music_working = 0;
                        music_address = 0;
                        error_working = 1;
                        error_address = 0;
                        warning_working = 0;
                        warning_address = 0;
                    end
                end
                // 优先级4：警告提示音模式 (warning = 1, error = 0, music = 0, no_response = 0)
                4'b0001: begin
                    if (~warning_working) begin
                        // 启动警告提示音播放
                        ms = 0;
                        music_working = 0;
                        music_address = 0;
                        error_working = 0;
                        error_address = 0;
                        warning_working = 1;
                        warning_address = 0;
                    end
                end
            endcase

            // 音频播放时序控制
            if (|{music_working, error_working, warning_working}) begin
                if (ms == ticking - 1) begin
                    // 时序计数器达到设定值，切换到下一个音符/音调
                    ms = 0;
                    case ({
                        music_working, error_working, warning_working
                    })
                        // 音乐播放控制
                        3'b100: begin
                            if (music_address >= $size(music_data) - 1) begin
                                // 音乐播放完毕，停止播放
                                music_working = 0;
                                music_address = 0;
                            end else begin
                                // 播放下一个音符
                                music_address = music_address + 1;
                            end
                        end
                        // 错误提示音播放控制
                        3'b010: begin
                            if (error_address >= $size(error_beep_data) - 1) begin
                                // 错误音播放完毕，停止播放
                                error_working = 0;
                                error_address = 0;
                            end else begin
                                // 播放下一个音调（蜂鸣音/静音切换）
                                error_address = error_address + 1;
                            end
                        end
                        // 警告提示音播放控制
                        3'b001: begin
                            if (warning_address >= $size(warning_beep_data) - 1) begin
                                // 警告音播放完毕，停止播放
                                warning_working = 0;
                                warning_address = 0;
                            end else begin
                                // 播放下一个音调（蜂鸣音/静音切换）
                                warning_address = warning_address + 1;
                            end
                        end
                    endcase
                end else begin
                    // 时序计数器递增
                    ms = ms + 1;
                end
            end
        end
    end

    // ========================================
    // 频率选择输出逻辑
    // ========================================

    /*
     * 根据当前播放状态选择相应的频率输出
     * - 音乐播放时：输出音乐数据中的频率值
     * - 错误音播放时：输出错误音数据中的频率值
     * - 警告音播放时：输出警告音数据中的频率值
     * - 无音频播放时：输出全1（静音状态）
     */
    always_comb begin
        case ({
            music_working, error_working, warning_working
        })
            3'b100:  frequency_select <= music_data[music_address];        // 输出音乐频率
            3'b010:  frequency_select <= error_beep_data[error_address];   // 输出错误音频率
            3'b001:  frequency_select <= warning_beep_data[warning_address]; // 输出警告音频率
            default: frequency_select <= ~0;                              // 静音状态（全1）
        endcase
    end

endmodule
