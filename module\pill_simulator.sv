/*
 * 药片传感器模拟器模块
 *
 * 功能描述：
 * - 模拟药片通过传感器时产生的脉冲信号
 * - 支持三种不同的装瓶速度模式
 * - 提供严格模式和正常模式两种工作方式
 * - 可以通过pill_disable信号控制药片传送的启停
 *
 * 速度模式说明：
 * - 慢速模式 (speed_select = 00): 2片/秒，适用于精确装瓶
 * - 中速模式 (speed_select = 01): 5片/秒，平衡速度和精度
 * - 快速模式 (speed_select = 10): 10片/秒，最大装瓶速度
 *
 * 工作模式说明：
 * - 严格模式 (strict_enable = 1): 无论pill_disable状态如何都产生脉冲
 * - 正常模式 (strict_enable = 0): 只有在pill_disable为0时才产生脉冲
 *
 * 作者：数字逻辑课程设计小组
 * 日期：2024-2025学年
 */

// 药片模拟器工作频率参数 (100MHz)
parameter pill_simulator_frequency = 100000000;

module pill_simulator(
    input logic clock,              // 系统时钟 (100MHz)
    input logic reset_n,            // 复位信号 (低电平有效)
    input logic strict_enable,      // 严格模式使能信号
    input logic pill_disable,       // 药片传送禁用信号
    input logic [1:0] speed_select, // 速度选择: 00=慢速(2/s), 01=中速(5/s), 10=快速(10/s)
    output logic pill_pulse         // 药片检测脉冲输出
);

    // ========================================
    // 输出使能控制逻辑
    // ========================================

    /*
     * 输出使能信号生成
     * - 严格模式下：无论pill_disable状态如何都输出脉冲
     * - 正常模式下：只有在pill_disable为0时才输出脉冲
     * 这样设计可以在测试时强制产生脉冲，在正常工作时受控制器管理
     */
    logic output_enable;
    assign output_enable = strict_enable || ~pill_disable;

    // ========================================
    // 速度配置逻辑
    // ========================================

    /*
     * 根据速度选择信号确定每秒药片数量
     * 支持三种预设速度模式，默认为中速模式
     */
    logic [31:0] pill_per_second;  // 每秒药片数量
    always_comb begin
        case (speed_select)
            2'b00: pill_per_second = 2;   // 慢速：2片/秒
            2'b01: pill_per_second = 5;   // 中速：5片/秒
            2'b10: pill_per_second = 10;  // 快速：10片/秒
            default: pill_per_second = 5; // 默认：中速模式
        endcase
    end

    // ========================================
    // 可变频率分频器
    // ========================================

    /*
     * 计算分频系数
     * 分频系数 = 系统时钟频率 / 目标频率
     * 例如：100MHz / 5Hz = 20,000,000
     */
    logic [31:0] divisor;  // 分频系数
    assign divisor = pill_simulator_frequency / pill_per_second;

    /*
     * 可变分频器实例
     * 根据计算出的分频系数生成相应频率的脉冲信号
     */
    logic pill_signal;  // 内部药片信号
    v_divider #(32) pill_divider_ins (
        .clock(clock),          // 输入时钟
        .reset_n(reset_n),      // 复位信号
        .divisor(divisor),      // 分频系数
        .clock_out(pill_signal) // 分频后的脉冲输出
    );

    // ========================================
    // 最终输出控制
    // ========================================

    /*
     * 根据输出使能信号控制最终的药片脉冲输出
     * - 使能时：输出分频器产生的脉冲信号
     * - 禁用时：输出恒定的低电平
     */
    assign pill_pulse = output_enable ? pill_signal : 1'b0;

endmodule
