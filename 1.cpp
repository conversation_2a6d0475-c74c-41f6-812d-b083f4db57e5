/*
 * 程序名称: 1.cpp - 动态二维数组矩阵运算
 * 功能描述: 实现两个4x5矩阵的加法和减法运算
 * 学习要点: 动态内存分配、二维指针、矩阵运算
 * 作者: 安奕轩 2023212334
 * 日期: 2024-2025学年
 */

#include<bits/stdc++.h>
using namespace std;

// 定义矩阵的行数和列数常量
const int rows = 4, cols = 5;

/**
 * 函数名: init
 * 功能: 动态分配二维数组内存并从用户输入初始化矩阵
 * 返回值: 指向动态分配的二维数组的指针
 * 学习要点:
 *   - 使用new操作符动态分配内存
 *   - 二维指针的使用方法
 *   - 嵌套循环处理二维数组
 */
int ** init()
{
    // 分配行指针数组
    int ** a = new int*[rows];

    // 为每一行分配内存并读取数据
    for(int i = 0; i < rows; i ++)
    {
        a[i] = new int[cols];  // 为第i行分配列空间
        for(int j = 0; j < cols; j ++)
            cin >> a[i][j];    // 读取矩阵元素
    }
    return a;
}

/**
 * 函数名: matrix_plus
 * 功能: 计算两个矩阵的加法
 * 参数: a, b - 两个输入矩阵的指针
 * 返回值: 指向结果矩阵的指针
 * 学习要点: 矩阵加法运算的实现
 */
int ** matrix_plus(int **a, int **b)
{
    cout << "A1+A2的结果为:" << endl;

    // 为结果矩阵分配内存
    int ** res = new int*[rows];
    for(int i = 0; i < rows; i ++)
    {
        res[i] = new int[cols];
        for(int j = 0; j < cols; j ++)
            res[i][j] = a[i][j] + b[i][j];  // 对应元素相加
    }
    return res;
}

/**
 * 函数名: matrix_minus
 * 功能: 计算两个矩阵的减法
 * 参数: a, b - 两个输入矩阵的指针
 * 返回值: 指向结果矩阵的指针
 * 学习要点: 矩阵减法运算的实现
 */
int ** matrix_minus(int **a, int **b)
{
    cout << "A1-A2的结果为:" << endl;

    // 为结果矩阵分配内存
    int ** res = new int*[rows];
    for(int i = 0; i < rows; i ++)
    {
        res[i] = new int[cols];
        for(int j = 0; j < cols; j ++)
            res[i][j] = a[i][j] - b[i][j];  // 对应元素相减
    }
    return res;
}

/**
 * 函数名: print
 * 功能: 打印矩阵内容
 * 参数: a - 要打印的矩阵指针
 * 学习要点: 二维数组的遍历和输出
 */
void print(int ** a)
{
    for(int i = 0; i < rows; i ++)
    {
        for(int j = 0; j < cols; j ++)
            cout << a[i][j] << ' ';  // 输出每个元素
        cout << endl;                // 每行结束后换行
    }
}

/**
 * 函数名: safe_delete_matrix
 * 功能: 安全释放二维动态数组内存
 * 参数: matrix - 要释放的二维数组指针
 * 学习要点:
 *   - 正确的内存释放顺序
 *   - 避免内存泄漏
 *   - 防止重复释放
 */
void safe_delete_matrix(int** &matrix) {
    if (matrix != nullptr) {
        for(int i = 0; i < rows; i++) {
            delete[] matrix[i];  // 先释放每行
        }
        delete[] matrix;  // 再释放行指针数组
        matrix = nullptr; // 防止重复释放
    }
}

/**
 * 主函数: 程序入口
 * 功能: 演示矩阵的加法和减法运算
 * 学习要点:
 *   - 动态内存管理
 *   - 函数调用和返回值处理
 *   - 内存泄漏问题（注意：此程序存在内存泄漏）
 */
int main()
{
    int **A1, **A2, **A3;

    // 输入第一个矩阵
    cout << "请输入矩阵A1:" << endl;
    A1 = init();

    // 输入第二个矩阵
    cout << "请输入矩阵A2:" << endl;
    A2 = init();

    // 执行矩阵加法并输出结果
    A3 = matrix_plus(A1, A2);
    print(A3);

    // 执行矩阵减法并输出结果
    // 注意：这里会造成内存泄漏，第一次matrix_plus的结果没有被释放
    // 正确做法：先释放之前的A3，再分配新的
    safe_delete_matrix(A3);  // 使用安全释放函数

    A3 = matrix_minus(A1, A2);
    print(A3);

    // === 错误的内存释放方式（已注释） ===
    // delete A1;  // 错误：只释放了指针数组，没有释放每行的内存
    // delete A2;  // 错误：只释放了指针数组，没有释放每行的内存
    // delete A3;  // 错误：只释放了指针数组，没有释放每行的内存

    // === 正确的内存释放方式（使用安全释放函数） ===
    safe_delete_matrix(A1);
    safe_delete_matrix(A2);
    safe_delete_matrix(A3);

    // === 原来的手动释放方式（已注释，但保留作为学习参考） ===
    // for(int i = 0; i < rows; i++) {
    //     delete[] A1[i];  // 先释放每行的内存
    // }
    // delete[] A1;  // 再释放行指针数组
    //
    // for(int i = 0; i < rows; i++) {
    //     delete[] A2[i];
    // }
    // delete[] A2;
    //
    // for(int i = 0; i < rows; i++) {
    //     delete[] A3[i];
    // }
    // delete[] A3;

    cout << "程序结束!内存空间已正确释放~" << endl;

    return 0;
}

/*
 * === 程序问题总结与解决方案 ===
 *
 * 原始问题：
 * 1. 内存释放方式错误：只释放了指针数组，没有释放每行的内存
 * 2. 存在内存泄漏：第一次matrix_plus的结果没有被释放就被覆盖了
 * 3. 没有空指针检查，可能导致重复释放
 *
 * 解决方案：
 * 1. 实现safe_delete_matrix函数，确保正确的释放顺序
 * 2. 在重新赋值前先释放旧内存
 * 3. 释放后将指针设为nullptr防止重复释放
 */