# Pill_device - 药片装瓶系统项目文件结构说明

## 项目目录结构

```
Pill_device/                            # 药片装瓶系统主目录
├── README.md                           # 项目主要说明文档
├── PROJECT_STRUCTURE.md               # 本文件：项目结构说明
├── PROJECT_RENAME_GUIDE.md            # 项目重命名指南
│
├── prj/                               # Vivado项目文件（不进行版本控制）
│   ├── Pill_device.cache/            # 项目缓存文件
│   ├── Pill_device.hw/               # 硬件配置文件
│   ├── Pill_device.ip_user_files/    # IP用户文件
│   ├── Pill_device.runs/             # 综合和实现运行文件
│   ├── Pill_device.sim/              # 仿真文件
│   ├── Pill_device.srcs/             # 源文件
│   ├── Pill_device.xpr               # Vivado项目文件
│   └── vivado*.log                   # Vivado日志文件
│
├── scripts/                           # TCL脚本目录
│   └── recreate_project.tcl           # 项目重建脚本
│
├── src/                               # 源代码目录
│   ├── hdl/                           # HDL源文件
│   │   ├── config.svh                 # 全局配置文件
│   │   ├── top_module.sv              # 顶层模块
│   │   └── controller/                # 控制器模块目录
│   │       ├── controller.sv          # 主控制器
│   │       ├── state_machine.sv       # 状态机
│   │       ├── input_receiver.sv      # 输入接收器
│   │       ├── data_modifier.sv       # 数据修改器
│   │       ├── pointer_modifier.sv    # 指针修改器
│   │       ├── display_data_generator.sv # 显示数据生成器
│   │       └── audio_output_generator.sv # 音频输出生成器
│   │
│   └── xdc/                           # 约束文件目录
│
├── synth_1/                           # 综合输出目录
│
├── ../module/                         # 通用模块目录（共享）
│   ├── audio_generator.sv             # 音频生成器
│   ├── buttons.sv                     # 按钮处理模块
│   ├── digital_tube.sv                # 数码管显示模块
│   ├── keyboards.sv                   # 键盘处理模块
│   ├── lcd_screen_signal_generator.sv # LCD屏幕信号生成器
│   └── pill_simulator.sv              # 药片模拟器
│
└── ../util/                           # 工具模块目录（共享）
    ├── combinational/                 # 组合逻辑模块
    │   └── segment_decoder.sv         # 7段数码管解码器
    │
    └── sequential/                    # 时序逻辑模块
        ├── counter.sv                 # 计数器
        ├── debouncer.sv               # 防抖动器
        ├── divider.sv                 # 分频器
        ├── synchronizer.sv            # 同步器
        ├── timing.sv                  # 时序控制器
        └── v_divider.sv               # 可变分频器
```

## 文件功能说明

### 核心模块文件

#### 1. 顶层模块 (`Pill_device/src/hdl/top_module.sv`)
- **功能**：系统最高层模块，连接所有子模块
- **特点**：
  - 定义系统输入输出接口
  - 实例化各个功能模块
  - 管理信号连接和分配
  - 提供清晰的模块层次结构

#### 2. 全局配置 (`Pill_device/src/hdl/config.svh`)
- **功能**：定义系统全局常量和枚举类型
- **内容**：
  - 系统状态枚举 (`state_t`)
  - 速度模式枚举 (`speed_t`)
  - 时钟频率常量
  - 其他系统参数

#### 3. 控制器模块 (`Pill_device/src/hdl/controller/`)

##### 3.1 主控制器 (`controller.sv`)
- **功能**：系统核心控制逻辑
- **特点**：
  - 协调各个子模块工作
  - 管理时序和数据流
  - 处理装瓶逻辑和计数

##### 3.2 状态机 (`state_machine.sv`)
- **功能**：管理系统五个主要状态
- **状态**：设置、工作、暂停、错误、完成
- **特点**：同步状态机，优先级明确

##### 3.3 输入接收器 (`input_receiver.sv`)
- **功能**：统一处理各种输入信号
- **特点**：
  - 按钮信号处理
  - 键盘信号处理
  - 药片脉冲信号处理

##### 3.4 数据修改器 (`data_modifier.sv`)
- **功能**：处理用户输入的参数设置
- **特点**：
  - 瓶数和药片数设置
  - 速度模式选择
  - 输入验证和错误检查

##### 3.5 指针修改器 (`pointer_modifier.sv`)
- **功能**：管理显示界面的光标位置
- **特点**：
  - 水平和垂直指针控制
  - 循环计数功能
  - 参数化设计

##### 3.6 显示数据生成器 (`display_data_generator.sv`)
- **功能**：生成数码管显示内容
- **特点**：
  - 根据状态生成不同显示
  - 支持闪烁效果
  - 多页面显示管理

##### 3.7 音频输出生成器 (`audio_output_generator.sv`)
- **功能**：根据系统状态选择音频输出
- **特点**：
  - 按键音反馈
  - 错误警告音
  - 完成庆祝音乐

### 通用模块文件

#### 1. 按钮处理 (`module/buttons.sv`)
- **功能**：处理物理按钮输入
- **特点**：
  - 信号同步化
  - 防抖动处理
  - 支持多个按钮

#### 2. 键盘处理 (`module/keyboards.sv`)
- **功能**：处理4x4矩阵键盘
- **特点**：
  - 矩阵扫描
  - 按键识别
  - 防重复触发

#### 3. 数码管显示 (`module/digital_tube.sv`)
- **功能**：驱动8位数码管显示
- **特点**：
  - 动态扫描显示
  - 7段编码转换
  - 小数点控制

#### 4. 音频生成器 (`module/audio_generator.sv`)
- **功能**：生成不同频率的音频信号
- **特点**：
  - 频率可选
  - PWM输出
  - 音调控制

#### 5. 药片模拟器 (`module/pill_simulator.sv`)
- **功能**：模拟药片传感器信号
- **特点**：
  - 三种速度模式
  - 可变频率输出
  - 使能控制

### 工具模块文件

#### 1. 组合逻辑模块 (`util/combinational/`)
- **segment_decoder.sv**：7段数码管解码器

#### 2. 时序逻辑模块 (`util/sequential/`)
- **counter.sv**：通用计数器
- **debouncer.sv**：防抖动器
- **divider.sv**：固定分频器
- **v_divider.sv**：可变分频器
- **synchronizer.sv**：信号同步器
- **timing.sv**：时序控制器

### 项目管理文件

#### 1. TCL脚本 (`Pill_device/scripts/recreate_project.tcl`)
- **功能**：Vivado项目重建脚本
- **用途**：版本控制和项目分享

#### 2. 约束文件 (`Pill_device/src/xdc/`)
- **功能**：FPGA引脚约束和时序约束
- **内容**：硬件接口定义

## 模块依赖关系

```
top_module.sv
├── config.svh (全局配置)
├── buttons.sv (按钮处理)
├── keyboards.sv (键盘处理)
├── pill_simulator.sv (药片模拟器)
├── controller.sv (主控制器)
│   ├── state_machine.sv
│   ├── input_receiver.sv
│   ├── data_modifier.sv
│   ├── pointer_modifier.sv
│   ├── display_data_generator.sv
│   └── audio_output_generator.sv
├── digital_tube.sv (数码管显示)
└── audio_generator.sv (音频生成器)
```

## 设计特点

1. **模块化设计**：每个功能独立成模块，便于维护和测试
2. **层次化结构**：清晰的模块层次，便于理解和扩展
3. **参数化设计**：使用参数提高模块的可重用性
4. **标准化接口**：统一的时钟、复位和使能信号
5. **详细注释**：每个模块都有完整的功能说明和注释
