/*
 * 按钮输入处理模块
 *
 * 功能描述：
 * - 处理5个物理按钮的输入信号
 * - 提供信号同步化功能，防止亚稳态
 * - 提供防抖动功能，消除按钮机械抖动
 * - 输出稳定可靠的按钮状态信号
 *
 * 按钮功能定义：
 * - button[0]: 向下/减少按钮
 * - button[1]: 向上/增加按钮
 * - button[2]: 向左按钮
 * - button[3]: 开始/暂停/确认按钮
 * - button[4]: 向右按钮
 *
 * 技术特性：
 * - 防抖动时间：10ms
 * - 支持5个独立按钮
 * - 每个按钮都有独立的防抖动电路
 *
 * 作者：数字逻辑课程设计小组
 * 日期：2024-2025学年
 */

`include "config.svh"

// 按钮模块参数定义
parameter buttons_frequency = 100000000;    // 工作频率 (100MHz)
parameter buttons_debounce_ms = 10;         // 防抖动时间 (10ms)
parameter buttons_number = 5;               // 按钮数量

module buttons (
    input logic clock,                                      // 系统时钟
    input logic reset_n,                                    // 复位信号 (低电平有效)
    input logic [buttons_number - 1 : 0] raw_button,       // 原始按钮输入信号
    output logic [buttons_number - 1 : 0] button           // 处理后的按钮输出信号
);

    // ========================================
    // 信号同步化处理
    // ========================================

    /*
     * 同步器模块实例
     * 功能：将异步的按钮输入信号同步到系统时钟域
     * 目的：防止亚稳态问题，确保信号的可靠性
     * 原理：使用两级触发器对输入信号进行同步
     */
    logic [buttons_number - 1 : 0] sync_button;  // 同步化后的按钮信号
    synchronizer #(
        .width(buttons_number)  // 同步器位宽，处理5个按钮
    ) sync_ins (
        .clock(clock),          // 系统时钟
        .reset_n(reset_n),      // 复位信号
        .in(raw_button),        // 原始按钮输入
        .out(sync_button)       // 同步化后的按钮输出
    );

    // ========================================
    // 防抖动处理
    // ========================================

    /*
     * 为每个按钮生成独立的防抖动电路
     *
     * 防抖动原理：
     * - 当按钮状态发生变化时，启动计时器
     * - 在防抖动时间内，如果信号保持稳定，则认为是有效的状态变化
     * - 如果在防抖动时间内信号再次变化，则重新开始计时
     *
     * 防抖动时间计算：
     * - 目标时间：10ms
     * - 时钟频率：100MHz
     * - 计数值：100,000,000 / (1000 / 10) = 1,000,000
     */
    generate
        for (genvar i = 0; i < buttons_number; i++) begin : debouncer_gen
            debouncer #(
                .frequency(buttons_frequency),  // 工作频率
                .modulus(buttons_frequency / (1000 / buttons_debounce_ms))  // 防抖动计数值
            ) deb_ins (
                .clock(clock),          // 系统时钟
                .reset_n(reset_n),      // 复位信号
                .in(sync_button[i]),    // 同步化后的按钮输入
                .out(button[i])         // 防抖动后的按钮输出
            );
        end
    endgenerate

endmodule
