/*
 * 数据修改器模块
 *
 * 功能描述：
 * - 处理用户输入的数字数据和速度设置
 * - 管理药瓶数量和每瓶药片数量的设置
 * - 实现键盘输入的重新映射和验证
 * - 控制装瓶速度的调节
 *
 * 数据管理：
 * - jar_cnt[0:3]: 药瓶数量的四位数字 (千位到个位)
 * - one_cnt[0:3]: 每瓶药片数量的四位数字 (千位到个位)
 * - speed_setting: 装瓶速度设置 (慢速/中速/快速)
 *
 * 键盘映射：
 * - 将4x4矩阵键盘重新映射为0-9数字和A-F字符
 * - 检测无效输入并产生警告信号
 *
 * 输入验证：
 * - 检测同时按下多个按键的情况
 * - 检测输入数字超出0-9范围的情况
 *
 * 作者：数字逻辑课程设计小组
 * 日期：2024-2025学年
 */

`include "config.svh"

module data_modifier (
    input logic clock,                      // 系统时钟
    input logic reset_n,                    // 复位信号 (低电平有效)
    input state_t state,                    // 当前系统状态
    input logic [1:0] h_ptr,                // 水平指针 (选择数字位置)
    input logic [2:0] v_ptr,                // 垂直指针 (选择设置项目)
    input logic [3:0][3:0] keyboard_down,   // 4x4键盘输入矩阵
    input logic speed_up_btn,               // 速度增加按钮
    input logic speed_down_btn,             // 速度减少按钮
    output int jar_cnt [0:3],               // 药瓶数量设置 (4位数字)
    output int one_cnt [0:3],               // 每瓶药片数量设置 (4位数字)
    output speed_t speed_setting,           // 装瓶速度设置
    output logic keyboard_warning           // 键盘输入警告信号
);
    // ========================================
    // 键盘输入重新映射
    // ========================================

    /*
     * 4x4矩阵键盘到16位向量的映射
     * 键盘布局：
     *   [0][0] [0][1] [0][2] [0][3]     1  2  3  A
     *   [1][0] [1][1] [1][2] [1][3] =>  4  5  6  B
     *   [2][0] [2][1] [2][2] [2][3]     7  8  9  C
     *   [3][0] [3][1] [3][2] [3][3]     F  0  E  D
     *
     * 重新映射为标准十六进制顺序：0-9, A-F
     */
    logic [15:0] reinterpreted_keyboard;

    // 数字0-9的映射
    assign reinterpreted_keyboard['h0] = keyboard_down[3][1];  // 数字0
    assign reinterpreted_keyboard['h1] = keyboard_down[0][0];  // 数字1
    assign reinterpreted_keyboard['h2] = keyboard_down[0][1];  // 数字2
    assign reinterpreted_keyboard['h3] = keyboard_down[0][2];  // 数字3
    assign reinterpreted_keyboard['h4] = keyboard_down[1][0];  // 数字4
    assign reinterpreted_keyboard['h5] = keyboard_down[1][1];  // 数字5
    assign reinterpreted_keyboard['h6] = keyboard_down[1][2];  // 数字6
    assign reinterpreted_keyboard['h7] = keyboard_down[2][0];  // 数字7
    assign reinterpreted_keyboard['h8] = keyboard_down[2][1];  // 数字8
    assign reinterpreted_keyboard['h9] = keyboard_down[2][2];  // 数字9

    // 字符A-F的映射
    assign reinterpreted_keyboard['ha] = keyboard_down[0][3];  // 字符A
    assign reinterpreted_keyboard['hb] = keyboard_down[1][3];  // 字符B
    assign reinterpreted_keyboard['hc] = keyboard_down[2][3];  // 字符C
    assign reinterpreted_keyboard['hd] = keyboard_down[3][3];  // 字符D
    assign reinterpreted_keyboard['he] = keyboard_down[3][2];  // 字符E
    assign reinterpreted_keyboard['hf] = keyboard_down[3][0];  // 字符F

    // ========================================
    // 键盘输入分析
    // ========================================

    int keyboard_count;                     // 同时按下的按键数量
    int keyboard_number;                    // 按下按键对应的数值

    /*
     * 计算同时按下的按键数量
     * 用于检测多键同时按下的错误情况
     */
    always_comb begin
        keyboard_count = 0;
        for (int i = 0; i < 16; i++)
            if (reinterpreted_keyboard[i])
                keyboard_count = keyboard_count + 1;
    end

    /*
     * 计算按下按键对应的数值
     * 如果同时按下多个按键，结果为所有按键值的和
     */
    always_comb begin
        keyboard_number = 0;
        for (int i = 0; i < 16; i++)
            if (reinterpreted_keyboard[i])
                keyboard_number = keyboard_number + i;
    end

    /*
     * 键盘输入警告检测
     * 警告条件：
     * 1. 同时按下多个按键 (keyboard_count > 1)
     * 2. 按下的按键值超出0-9范围 (keyboard_number > 9)
     * 只在设置状态下进行检测
     */
    assign keyboard_warning = (state == setting_state) && ((keyboard_count > 1) || (keyboard_number > 9));

    // ========================================
    // 数据设置和速度控制逻辑
    // ========================================

    /*
     * 主要功能：
     * 1. 管理药瓶数量和每瓶药片数量的设置
     * 2. 控制装瓶速度的调节
     * 3. 处理键盘数字输入
     */
    always @(posedge clock or negedge reset_n) begin
        if (~ reset_n) begin
            // 复位时初始化所有数据
            for (int i = 0; i < 4; i++) begin
                jar_cnt[i] <= 0;        // 药瓶数量清零
                one_cnt[i] <= 0;        // 每瓶药片数量清零
            end
            speed_setting <= speed_mid; // 默认中等速度
        end else begin
            // 速度控制逻辑 (仅在设置状态下有效)
            if (state == setting_state) begin
                if (speed_up_btn) begin
                    // 速度增加按钮按下
                    case (speed_setting)
                        speed_slow: speed_setting <= speed_mid;   // 慢速 -> 中速
                        speed_mid:  speed_setting <= speed_fast;  // 中速 -> 快速
                        speed_fast: speed_setting <= speed_fast;  // 快速保持不变
                        default:    speed_setting <= speed_mid;   // 默认中速
                    endcase
                end else if (speed_down_btn) begin
                    // 速度减少按钮按下
                    case (speed_setting)
                        speed_slow: speed_setting <= speed_slow; // 慢速保持不变
                        speed_mid:  speed_setting <= speed_slow; // 中速 -> 慢速
                        speed_fast: speed_setting <= speed_mid;  // 快速 -> 中速
                        default:    speed_setting <= speed_mid;  // 默认中速
                    endcase
                end
            end

            // 数字输入逻辑 (仅在设置状态下，单键按下，且为有效数字时)
            if (state == setting_state && keyboard_count == 1 && keyboard_number <= 9) begin
                case (v_ptr)
                    0: jar_cnt[h_ptr] = keyboard_number;  // 药瓶数量设置
                    1: one_cnt[h_ptr] = keyboard_number;  // 每瓶药片数量设置
                    2: begin end  // 速度设置页面 - 不需要数字输入
                    3: jar_cnt[h_ptr] = keyboard_number;  // 药瓶数量设置 (重复页面)
                    4: one_cnt[h_ptr] = keyboard_number;  // 每瓶药片数量设置 (重复页面)
                    5: begin end  // 速度设置页面 (重复) - 不需要数字输入
                endcase
            end
        end
    end

endmodule
