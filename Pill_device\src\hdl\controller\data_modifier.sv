`include "config.svh"

module data_modifier (
    input logic clock,
    input logic reset_n,
    input state_t state,
    input logic [1:0] h_ptr,
    input logic [2:0] v_ptr,
    input logic [3:0][3:0] keyboard_down,
    input logic speed_up_btn,
    input logic speed_down_btn,
    output int jar_cnt [0:3],
    output int one_cnt [0:3],
    output speed_t speed_setting,
    output logic keyboard_warning
);
    logic [15:0] reinterpreted_keyboard;

    assign reinterpreted_keyboard['h0] = keyboard_down[3][1];
    assign reinterpreted_keyboard['h1] = keyboard_down[0][0];
    assign reinterpreted_keyboard['h2] = keyboard_down[0][1];
    assign reinterpreted_keyboard['h3] = keyboard_down[0][2];
    assign reinterpreted_keyboard['h4] = keyboard_down[1][0];
    assign reinterpreted_keyboard['h5] = keyboard_down[1][1];
    assign reinterpreted_keyboard['h6] = keyboard_down[1][2];
    assign reinterpreted_keyboard['h7] = keyboard_down[2][0];
    assign reinterpreted_keyboard['h8] = keyboard_down[2][1];
    assign reinterpreted_keyboard['h9] = keyboard_down[2][2];
    assign reinterpreted_keyboard['ha] = keyboard_down[0][3];
    assign reinterpreted_keyboard['hb] = keyboard_down[1][3];
    assign reinterpreted_keyboard['hc] = keyboard_down[2][3];
    assign reinterpreted_keyboard['hd] = keyboard_down[3][3];
    assign reinterpreted_keyboard['he] = keyboard_down[3][2];
    assign reinterpreted_keyboard['hf] = keyboard_down[3][0];

    int keyboard_count;
    int keyboard_number;

    always_comb begin
        keyboard_count = 0;
        for (int i = 0; i < 16; i++)
            if (reinterpreted_keyboard[i])
                keyboard_count = keyboard_count + 1;
    end

    always_comb begin
        keyboard_number = 0;
        for (int i = 0; i < 16; i++)
            if (reinterpreted_keyboard[i])
                keyboard_number = keyboard_number + i;
    end

    assign keyboard_warning = (state == setting_state) && ((keyboard_count > 1) || (keyboard_number > 9));

    always @(posedge clock or negedge reset_n) begin
        if (~ reset_n) begin
            for (int i = 0; i < 4; i++) begin
                jar_cnt[i] <= 0;
                one_cnt[i] <= 0;
            end
            speed_setting <= speed_mid; // default to medium speed
        end else begin
            // Speed control logic
            if (state == setting_state) begin
                if (speed_up_btn) begin
                    case (speed_setting)
                        speed_slow: speed_setting <= speed_mid;
                        speed_mid:  speed_setting <= speed_fast;
                        speed_fast: speed_setting <= speed_fast; // stay at fast
                        default:    speed_setting <= speed_mid;
                    endcase
                end else if (speed_down_btn) begin
                    case (speed_setting)
                        speed_slow: speed_setting <= speed_slow; // stay at slow
                        speed_mid:  speed_setting <= speed_slow;
                        speed_fast: speed_setting <= speed_mid;
                        default:    speed_setting <= speed_mid;
                    endcase
                end
            end

            // Number input logic
            if (state == setting_state && keyboard_count == 1 && keyboard_number <= 9) begin
                case (v_ptr)
                    0: jar_cnt[h_ptr] = keyboard_number;  // JAR setting
                    1: one_cnt[h_ptr] = keyboard_number;  // PILL setting
                    2: begin end  // Speed setting - no number input needed
                    3: jar_cnt[h_ptr] = keyboard_number;  // JAR setting (repeat)
                    4: one_cnt[h_ptr] = keyboard_number;  // PILL setting (repeat)
                    5: begin end  // Speed setting (repeat) - no number input needed
                endcase
            end
        end
    end

endmodule
