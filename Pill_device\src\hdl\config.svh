/*
 * 药片装瓶系统全局配置文件
 *
 * 功能描述：
 * - 定义系统中使用的所有枚举类型
 * - 定义系统全局常量和参数
 * - 提供统一的配置管理
 *
 * 作者：数字逻辑课程设计小组
 * 日期：2024-2025学年
 */

`ifndef _GLOBAL_CONFIGURATION_H_
`define _GLOBAL_CONFIGURATION_H_

// ========================================
// 系统状态枚举定义
// ========================================

/*
 * 系统主状态机状态定义
 *
 * setting_state: 参数设置状态
 *   - 用户可以设置瓶数、每瓶药片数、装瓶速度
 *   - 系统等待用户输入和确认
 *
 * working_state: 正常工作状态
 *   - 系统按照设定参数自动装瓶
 *   - 监控药片传感器信号
 *   - 实时更新显示信息
 *
 * pause_state: 暂停状态
 *   - 暂时停止装瓶过程
 *   - 保持当前进度不变
 *   - 可以恢复到工作状态
 *
 * error_state: 错误状态
 *   - 检测到系统异常时进入
 *   - 停止所有装瓶操作
 *   - 需要用户干预处理
 *
 * final_state: 完成状态
 *   - 所有瓶子装瓶完成
 *   - 播放完成提示音
 *   - 显示最终统计信息
 */
typedef enum logic [2:0] {
    setting_state,  // 参数设置状态
    working_state,  // 正常工作状态
    pause_state,    // 暂停状态
    error_state,    // 错误状态
    final_state     // 完成状态
} state_t;

// ========================================
// 装瓶速度枚举定义
// ========================================

/*
 * 装瓶速度选择枚举
 *
 * speed_slow: 慢速模式 - 2片/秒
 *   - 适用于精确装瓶要求
 *   - 降低药片卡住的风险
 *
 * speed_mid: 中速模式 - 5片/秒
 *   - 平衡速度和精度
 *   - 默认推荐速度
 *
 * speed_fast: 快速模式 - 10片/秒
 *   - 最大装瓶速度
 *   - 适用于大批量生产
 */
typedef enum logic [1:0] {
    speed_slow,   // 慢速：2片/秒
    speed_mid,    // 中速：5片/秒
    speed_fast    // 快速：10片/秒
} speed_t;

// ========================================
// 系统全局常量定义
// ========================================

// 系统主时钟频率 (Hz)
// Minisys实验板的主时钟频率为100MHz
`define frequency 100000000

// 显示刷新相关参数
// 数码管显示刷新分频系数
`define frequency_fliping 4

// 音频输出相关参数
// 音频信号位宽参数
`define audio_bps 4

`endif
