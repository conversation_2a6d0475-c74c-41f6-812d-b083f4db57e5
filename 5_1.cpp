/*
 * 程序名称: 5_1.cpp - 虚函数与多态的正确实现
 * 功能描述: 改进版的Shape继承体系，正确使用虚函数和纯虚函数实现多态
 * 学习要点: 虚函数、纯虚函数、虚析构函数、override关键字、多态
 * 作者: 学生
 * 日期: 2024-2025学年
 *
 * 与3.cpp的区别：
 * 1. 使用了虚析构函数确保正确的多态析构
 * 2. 使用了纯虚函数使Shape成为抽象基类
 * 3. 使用了override关键字确保正确重写虚函数
 */

#include<bits/stdc++.h>
using namespace std;

// 定义圆周率常量
const double PIE = acos(-1);

/**
 * 类名: Shape
 * 功能: 几何图形的抽象基类
 * 学习要点:
 *   - 抽象基类的设计
 *   - 纯虚函数的使用
 *   - 虚析构函数的重要性
 */
class Shape
{
    public:
        /**
         * 构造函数
         * 功能: 基类构造函数
         */
        Shape() {
            cout << "Shape构造函数" << endl;
        }

        /**
         * 虚析构函数
         * 功能: 确保通过基类指针删除派生类对象时能正确调用派生类析构函数
         * 学习要点: 虚析构函数在多态中的重要性
         */
        virtual ~Shape() {
            cout << "Shape析构函数" << endl;
        }

        /**
         * 纯虚函数: area
         * 功能: 计算图形面积的接口
         * 学习要点:
         *   - 纯虚函数使类成为抽象类
         *   - 派生类必须实现纯虚函数
         *   - 实现真正的多态
         */
        virtual double area() const = 0;
};

/**
 * 类名: Rectangle
 * 功能: 矩形类，继承自Shape
 * 学习要点:
 *   - 虚函数的重写
 *   - override关键字的使用
 */
class Rectangle: public Shape
{
    protected:
        double length, width;

    public:
        /**
         * 构造函数
         * 参数: l - 长度, w - 宽度
         */
        Rectangle(double l, double w) : length(l), width(w) {
            cout << "Rectangle构造函数" << endl;
        }

        /**
         * 虚析构函数重写
         * 学习要点: override关键字确保正确重写
         */
        ~Rectangle() override {
            cout << "Rectangle析构函数" << endl;
        }

        /**
         * 虚函数重写: area
         * 功能: 计算矩形面积
         * 学习要点: override关键字的使用
         */
        double area() const override {
            return length * width;
        }
};

/**
 * 类名: Circle
 * 功能: 圆形类，继承自Shape
 */
class Circle: public Shape
{
    private:
        double radius;

    public:
        /**
         * 构造函数
         * 参数: r - 半径
         */
        Circle(double r) : radius(r) {
            cout << "Circle构造函数" << endl;
        }

        /**
         * 虚析构函数重写
         */
        ~Circle() override {
            cout << "Circle析构函数" << endl;
        }

        /**
         * 虚函数重写: area
         * 功能: 计算圆形面积
         */
        double area() const override {
            return PIE * radius * radius;
        }
};

/**
 * 类名: Square
 * 功能: 正方形类，继承自Rectangle
 * 学习要点: 多级继承中的虚函数
 */
class Square: public Rectangle
{
    public:
        /**
         * 构造函数
         * 参数: side - 边长
         */
        Square(double side) : Rectangle(side, side) {
            cout << "Square构造函数" << endl;
        }

        /**
         * 虚析构函数重写
         */
        ~Square() override {
            cout << "Square析构函数" << endl;
        }

        // 注意：Square继承了Rectangle的area()函数，无需重新实现
        // 但如果需要，也可以重写area()函数
};

/**
 * 主函数: 程序入口
 * 功能: 演示虚函数和多态的使用
 * 学习要点:
 *   - 抽象基类不能实例化
 *   - 虚函数的动态绑定
 *   - 多态的实现和应用
 *   - 虚析构函数的作用
 */
int main()
{
    // 注意：Shape是抽象类，不能直接实例化
    // Shape shape; // 这行代码会编译错误

    double a, b;
    cout << "---创建对象---" << endl;

    cout << "请输入长方形长和宽:" << endl;
    cin >> a >> b;
    Rectangle rect(a, b);  // 调用顺序：Shape() → Rectangle()

    cout << "请输入圆形半径:" << endl;
    cin >> a;
    Circle cir(a);  // 调用顺序：Shape() → Circle()

    cout << "请输入正方形边长:" << endl;
    cin >> a;
    Square squ(a);  // 调用顺序：Shape() → Rectangle() → Square()

    // 计算面积演示多态
    cout << "---面积计算（多态演示）---" << endl;
    cout << "长方形面积为:" << rect.area() << endl;  // 调用Rectangle::area()
    cout << "圆形面积为:" << cir.area() << endl;     // 调用Circle::area()
    cout << "正方形面积为:" << squ.area() << endl;   // 调用Rectangle::area()（继承）

    // 演示多态的威力：通过基类指针调用虚函数
    cout << "---通过基类指针演示多态---" << endl;
    Shape* shapes[] = {&rect, &cir, &squ};
    for(int i = 0; i < 3; i++) {
        cout << "图形" << (i+1) << "的面积为:" << shapes[i]->area() << endl;
        // 这里会根据实际对象类型调用相应的area()函数（动态绑定）
    }

    return 0;
    // 析构顺序：
    // Square() → Rectangle() → Shape()
    // Circle() → Shape()
    // Rectangle() → Shape()
}