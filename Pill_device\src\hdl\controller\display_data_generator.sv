/*
 * 显示数据生成器模块
 *
 * 功能描述：
 * - 根据系统状态和用户选择生成7段显示器的显示内容
 * - 管理不同页面的显示切换和数据格式化
 * - 提供用户界面的视觉反馈和指针指示
 *
 * 显示模式：
 * 1. 设置状态：显示药瓶数量、每瓶药片数量、速度设置
 * 2. 工作状态：显示总药片数、当前瓶药片数、当前瓶号、设置值、速度
 * 3. 错误状态：显示全空白
 * 4. 完成状态：同工作状态显示
 *
 * 页面切换 (v_ptr控制)：
 * 0: 药瓶数量设置/总药片数显示
 * 1: 每瓶药片数量设置/当前瓶药片数显示
 * 2: 速度设置/速度显示
 * 3-5: 重复显示页面0-2
 *
 * 指针指示：
 * - 在设置状态下，通过闪烁显示当前选中的数字位置
 * - 使用flip信号控制闪烁频率
 *
 * 作者：数字逻辑课程设计小组
 * 日期：2024-2025学年
 */

`include "config.svh"

module display_data_generator (
    input logic clock,                      // 系统时钟
    input logic reset_n,                    // 复位信号 (低电平有效)
    input state_t state,                    // 当前系统状态
    input logic [1:0] h_ptr,                // 水平指针 (数字位置选择)
    input logic [2:0] v_ptr,                // 垂直指针 (页面选择)
    input int jar_number,                   // 药瓶总数设置
    input int one_number,                   // 每瓶药片数设置
    input int now_count,                    // 当前瓶中药片计数
    input int now_jar_count,                // 当前已装瓶数量
    input int total_pill,                   // 总药片计数
    input speed_t speed_setting,            // 装瓶速度设置

    output logic [7:0][5:0] display_code,   // 8位7段显示器编码输出
    output logic [7:0] display_dp           // 8位7段显示器小数点输出
);

    // 小数点输出固定为0 (不使用小数点显示)
    assign display_dp = 0;

    // ========================================
    // 闪烁控制信号生成
    // ========================================

    /*
     * 生成闪烁信号用于指针指示
     * - 分频器产生约0.1秒的闪烁周期
     * - 在设置状态下用于指示当前选中的数字位置
     */
    logic flip;                             // 闪烁控制信号
    divider #(10000000) flip_ins (
        .clock(clock),
        .reset_n(reset_n),
        .clock_out(flip)
    );

    // ========================================
    // 显示内容生成逻辑
    // ========================================

    /*
     * 主要显示控制逻辑
     * - 根据系统状态选择显示模式
     * - 根据垂直指针选择显示页面
     * - 在设置状态下提供闪烁指针指示
     */
    always_ff @(posedge clock or negedge reset_n) begin
        if (~ reset_n) begin
            // 复位时显示全空白
            display_code <= ~ 0;
        end else begin
            // 默认显示全空白，然后根据状态设置具体内容
            display_code = ~ 0;
            case (state)
                // ========================================
                // 设置状态显示
                // ========================================
                setting_state:
                    case (v_ptr)
                        // 页面0：药瓶数量设置 (JAR xxxx)
                        0: begin
                            display_code[7] = 6'b010011; // 显示字符 'J'
                            display_code[6] = 6'b001010; // 显示字符 'A'
                            display_code[5] = 6'b011011; // 显示字符 'R'
                            // 显示位4空白
                            display_code[3] = jar_number / 1000;        // 千位数字
                            display_code[2] = jar_number / 100 % 10;    // 百位数字
                            display_code[1] = jar_number / 10 % 10;     // 十位数字
                            display_code[0] = jar_number / 1 % 10;      // 个位数字
                            if (flip)
                                display_code[h_ptr] = ~ 0;              // 闪烁指示当前选中位置
                        end
                        // 页面1：每瓶药片数量设置 (PILL xxxx)
                        1: begin
                            display_code[7] = 6'b011001; // 显示字符 'P'
                            display_code[6] = 6'b010010; // 显示字符 'I'
                            display_code[5] = 6'b010101; // 显示字符 'L'
                            display_code[4] = 6'b010101; // 显示字符 'L'
                            display_code[3] = one_number / 1000;        // 千位数字
                            display_code[2] = one_number / 100 % 10;    // 百位数字
                            display_code[1] = one_number / 10 % 10;     // 十位数字
                            display_code[0] = one_number / 1 % 10;      // 个位数字
                            if (flip)
                                display_code[h_ptr] = ~ 0;              // 闪烁指示当前选中位置
                        end
                        // 页面2：装瓶速度设置
                        2: begin
                            // 根据速度设置显示相应文字
                            case (speed_setting)
                                speed_slow: begin
                                    display_code[7] = ~ 0;       // 空白
                                    display_code[6] = 6'b011100; // 显示字符 'S'
                                    display_code[5] = 6'b010101; // 显示字符 'L'
                                    display_code[4] = 6'b011000; // 显示字符 'O' (显示 "SLO")
                                end
                                speed_mid: begin
                                    display_code[7] = ~ 0;       // 空白
                                    display_code[6] = 6'b010110; // 显示字符 'M'
                                    display_code[5] = 6'b010010; // 显示字符 'I'
                                    display_code[4] = 6'b001101; // 显示字符 'D' (显示 "MID")
                                end
                                speed_fast: begin
                                    display_code[7] = 6'b001111; // 显示字符 'F'
                                    display_code[6] = 6'b001010; // 显示字符 'A'
                                    display_code[5] = 6'b011100; // 显示字符 'S'
                                    display_code[4] = 6'b011101; // 显示字符 'T' (显示 "FAST")
                                end
                                default: begin
                                    display_code[7] = ~ 0;       // 空白
                                    display_code[6] = 6'b010110; // 显示字符 'M'
                                    display_code[5] = 6'b010010; // 显示字符 'I'
                                    display_code[4] = 6'b001101; // 显示字符 'D' (默认显示 "MID")
                                end
                            endcase
                            // 右侧四位显示空白
                            display_code[3] = ~ 0; // 空白
                            display_code[2] = ~ 0; // 空白
                            display_code[1] = ~ 0; // 空白
                            display_code[0] = ~ 0; // 空白
                        end
                        // 页面3：药瓶数量设置 (重复页面0)
                        3: begin
                            display_code[7] = 6'b010011; // 显示字符 'J'
                            display_code[6] = 6'b001010; // 显示字符 'A'
                            display_code[5] = 6'b011011; // 显示字符 'R'
                            // 显示位4空白
                            display_code[3] = jar_number / 1000;        // 千位数字
                            display_code[2] = jar_number / 100 % 10;    // 百位数字
                            display_code[1] = jar_number / 10 % 10;     // 十位数字
                            display_code[0] = jar_number / 1 % 10;      // 个位数字
                            if (flip)
                                display_code[h_ptr] = ~ 0;              // 闪烁指示当前选中位置
                        end
                        // 页面4：每瓶药片数量设置 (重复页面1)
                        4: begin
                            display_code[7] = 6'b011001; // 显示字符 'P'
                            display_code[6] = 6'b010010; // 显示字符 'I'
                            display_code[5] = 6'b010101; // 显示字符 'L'
                            display_code[4] = 6'b010101; // 显示字符 'L'
                            display_code[3] = one_number / 1000;        // 千位数字
                            display_code[2] = one_number / 100 % 10;    // 百位数字
                            display_code[1] = one_number / 10 % 10;     // 十位数字
                            display_code[0] = one_number / 1 % 10;      // 个位数字
                            if (flip)
                                display_code[h_ptr] = ~ 0;              // 闪烁指示当前选中位置
                        end
                        // 页面5：装瓶速度设置 (重复页面2)
                        5: begin
                            // 根据速度设置显示相应文字
                            case (speed_setting)
                                speed_slow: begin
                                    display_code[7] = ~ 0;       // 空白
                                    display_code[6] = 6'b011100; // 显示字符 'S'
                                    display_code[5] = 6'b010101; // 显示字符 'L'
                                    display_code[4] = 6'b011000; // 显示字符 'O' (显示 "SLO")
                                end
                                speed_mid: begin
                                    display_code[7] = ~ 0;       // 空白
                                    display_code[6] = 6'b010110; // 显示字符 'M'
                                    display_code[5] = 6'b010010; // 显示字符 'I'
                                    display_code[4] = 6'b001101; // 显示字符 'D' (显示 "MID")
                                end
                                speed_fast: begin
                                    display_code[7] = 6'b001111; // 显示字符 'F'
                                    display_code[6] = 6'b001010; // 显示字符 'A'
                                    display_code[5] = 6'b011100; // 显示字符 'S'
                                    display_code[4] = 6'b011101; // 显示字符 'T' (显示 "FAST")
                                end
                                default: begin
                                    display_code[7] = ~ 0;       // 空白
                                    display_code[6] = 6'b010110; // 显示字符 'M'
                                    display_code[5] = 6'b010010; // 显示字符 'I'
                                    display_code[4] = 6'b001101; // 显示字符 'D' (默认显示 "MID")
                                end
                            endcase
                            // 右侧四位显示空白
                            display_code[3] = ~ 0; // 空白
                            display_code[2] = ~ 0; // 空白
                            display_code[1] = ~ 0; // 空白
                            display_code[0] = ~ 0; // 空白
                        end
                        // 默认页面：药瓶数量设置
                        default: begin
                            display_code[7] = 6'b010011; // 显示字符 'J'
                            display_code[6] = 6'b001010; // 显示字符 'A'
                            display_code[5] = 6'b011011; // 显示字符 'R'
                            // 显示位4空白
                            display_code[3] = jar_number / 1000;        // 千位数字
                            display_code[2] = jar_number / 100 % 10;    // 百位数字
                            display_code[1] = jar_number / 10 % 10;     // 十位数字
                            display_code[0] = jar_number / 1 % 10;      // 个位数字
                            if (flip)
                                display_code[h_ptr] = ~ 0;              // 闪烁指示当前选中位置
                        end
                    endcase
                error_state:
                    display_code = ~ 0; // do nothing;
                default:
                    case (v_ptr)
                        0: begin
                            display_code[7] = total_pill / 10000000 % 10;
                            display_code[6] = total_pill / 1000000 % 10;
                            display_code[5] = total_pill / 100000 % 10;
                            display_code[4] = total_pill / 10000 % 10;
                            display_code[3] = total_pill / 1000 % 10;
                            display_code[2] = total_pill / 100 % 10;
                            display_code[1] = total_pill / 10 % 10;
                            display_code[0] = total_pill / 1 % 10;
                        end
                        1: begin
                            display_code[7] = 6'b011001; // P

                            display_code[3] = now_count / 1000 % 10;
                            display_code[2] = now_count / 100 % 10;
                            display_code[1] = now_count / 10 % 10;
                            display_code[0] = now_count / 1 % 10;
                        end
                        2: begin
                            display_code[7] = 6'b010111; // N
                            display_code[6] = 6'b011000; // O

                            display_code[3] = now_jar_count / 1000 % 10;
                            display_code[2] = now_jar_count / 100 % 10;
                            display_code[1] = now_jar_count / 10 % 10;
                            display_code[0] = now_jar_count / 1 % 10;
                        end
                        3: begin
                            display_code[7] = 6'b010011; // J
                            display_code[6] = 6'b001010; // A
                            display_code[5] = 6'b011011; // R

                            display_code[3] = jar_number / 1000;
                            display_code[2] = jar_number / 100 % 10;
                            display_code[1] = jar_number / 10 % 10;
                            display_code[0] = jar_number / 1 % 10;
                        end
                        4: begin
                            display_code[7] = 6'b011001; // P
                            display_code[6] = 6'b010010; // I
                            display_code[5] = 6'b010101; // L
                            display_code[4] = 6'b010101; // L

                            display_code[3] = one_number / 1000;
                            display_code[2] = one_number / 100 % 10;
                            display_code[1] = one_number / 10 % 10;
                            display_code[0] = one_number / 1 % 10;
                        end
                        5: begin
                            // Speed display in working state
                            case (speed_setting)
                                speed_slow: begin
                                    display_code[7] = ~ 0;       // blank
                                    display_code[6] = 6'b011100; // S
                                    display_code[5] = 6'b010101; // L
                                    display_code[4] = 6'b011000; // O
                                end
                                speed_mid: begin
                                    display_code[7] = ~ 0;       // blank
                                    display_code[6] = 6'b010110; // M
                                    display_code[5] = 6'b010010; // I
                                    display_code[4] = 6'b001101; // D
                                end
                                speed_fast: begin
                                    display_code[7] = 6'b001111; // F
                                    display_code[6] = 6'b001010; // A
                                    display_code[5] = 6'b011100; // S
                                    display_code[4] = 6'b011101; // T
                                end
                                default: begin
                                    display_code[7] = ~ 0;       // blank
                                    display_code[6] = 6'b010110; // M
                                    display_code[5] = 6'b010010; // I
                                    display_code[4] = 6'b001101; // D
                                end
                            endcase
                            display_code[3] = ~ 0; // blank
                            display_code[2] = ~ 0; // blank
                            display_code[1] = ~ 0; // blank
                            display_code[0] = ~ 0; // blank
                        end
                    endcase
            endcase
        end
    end

endmodule
