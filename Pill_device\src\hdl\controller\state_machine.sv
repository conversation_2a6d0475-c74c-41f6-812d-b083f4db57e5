/*
 * 药片装瓶系统状态机模块
 *
 * 功能描述：
 * - 管理系统的五个主要状态转换
 * - 根据输入信号控制状态切换
 * - 确保系统状态的正确性和一致性
 *
 * 状态转换逻辑：
 * setting_state -> working_state: 用户按下开始按钮
 * working_state -> pause_state: 用户按下暂停按钮
 * working_state -> error_state: 检测到错误信号
 * working_state -> final_state: 装瓶完成信号
 * pause_state -> working_state: 用户按下继续按钮
 * pause_state -> error_state: 检测到错误信号
 * pause_state -> final_state: 装瓶完成信号
 * error_state -> setting_state: 用户按下复位按钮
 * final_state -> setting_state: 用户按下复位按钮
 *
 * 作者：数字逻辑课程设计小组
 * 日期：2024-2025学年
 */

`include "config.svh"

module state_machine (
    input logic clock,              // 系统时钟
    input logic reset_n,            // 复位信号 (低电平有效)
    input logic switch_signal,      // 状态切换信号 (来自按钮3)
    input logic error_signal,       // 错误检测信号
    input logic complete_signal,    // 装瓶完成信号
    output state_t state           // 当前系统状态输出
);

    // ========================================
    // 状态机主逻辑
    // ========================================

    /*
     * 同步状态机实现
     * - 时钟上升沿触发状态更新
     * - 异步复位到设置状态
     * - 根据当前状态和输入信号决定下一状态
     */
    always_ff @(posedge clock or negedge reset_n) begin
        if (~reset_n) begin
            // 异步复位：系统复位时回到设置状态
            state <= setting_state;
        end
        else begin
            // 根据当前状态和输入信号进行状态转换
            case (state)

                // 设置状态：等待用户确认开始装瓶
                setting_state: begin
                    if (switch_signal)
                        state <= working_state;  // 按下开始按钮，进入工作状态
                    else
                        state <= setting_state;  // 继续等待用户输入
                end

                // 工作状态：正常装瓶过程
                working_state: begin
                    if (error_signal) begin
                        // 优先级1：检测到错误，立即进入错误状态
                        state <= error_state;
                    end
                    else if (complete_signal) begin
                        // 优先级2：装瓶完成，进入完成状态
                        state <= final_state;
                    end
                    else if (switch_signal) begin
                        // 优先级3：用户按下暂停按钮，进入暂停状态
                        state <= pause_state;
                    end
                    else begin
                        // 默认：保持工作状态
                        state <= working_state;
                    end
                end

                // 暂停状态：暂时停止装瓶，可以恢复
                pause_state: begin
                    if (error_signal) begin
                        // 优先级1：检测到错误，立即进入错误状态
                        state <= error_state;
                    end
                    else if (complete_signal) begin
                        // 优先级2：装瓶完成，进入完成状态
                        state <= final_state;
                    end
                    else if (switch_signal) begin
                        // 优先级3：用户按下继续按钮，恢复工作状态
                        state <= working_state;
                    end
                    else begin
                        // 默认：保持暂停状态
                        state <= pause_state;
                    end
                end

                // 错误状态：系统异常，需要用户干预
                error_state: begin
                    if (switch_signal)
                        state <= setting_state;  // 按下复位按钮，回到设置状态
                    else
                        state <= error_state;    // 继续保持错误状态
                end

                // 完成状态：所有装瓶任务完成
                final_state: begin
                    if (switch_signal)
                        state <= setting_state;  // 按下复位按钮，回到设置状态
                    else
                        state <= final_state;    // 继续保持完成状态
                end

                // 默认情况：防止进入未定义状态
                default: begin
                    state <= setting_state;     // 安全回到设置状态
                end

            endcase
        end
    end

endmodule
