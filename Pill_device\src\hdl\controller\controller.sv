/*
 * 药片装瓶系统主控制器模块
 *
 * 功能描述：
 * - 整合所有子模块，实现完整的药片装瓶控制系统
 * - 管理输入处理、状态控制、数据处理和输出生成
 * - 协调各个功能模块之间的数据流和控制信号
 *
 * 系统架构：
 * Part 0: 输入处理 - 处理键盘、按钮和传感器输入
 * Part 1: 状态管理 - 状态机和指针控制
 * Part 2: 数据处理 - 装瓶逻辑和计数管理
 * Part 3: 输出生成 - 显示和音频输出
 *
 * 主要功能：
 * - 药瓶数量和每瓶药片数量的设置
 * - 装瓶过程的自动控制和监控
 * - 错误检测和处理
 * - 用户界面显示和音频反馈
 *
 * 作者：数字逻辑课程设计小组
 * 日期：2024-2025学年
 */

`include "config.svh"

module controller (
    input logic clock,                      // 系统主时钟
    input logic reset_n,                    // 复位信号 (低电平有效)
    input logic [15:0] keyboard,            // 16位键盘输入
    input logic [4:0] button,               // 5位按钮输入
    input logic pill,                       // 药片传感器输入

    output logic [7:0][5:0] display_code,   // 8位7段显示器编码输出
    output logic [7:0] display_dp,          // 8位7段显示器小数点输出
    output logic [31:0] frequency_select,   // 音频频率选择输出
    output logic funnel_disable,            // 漏斗禁用控制输出
    output logic [1:0] speed_select         // 装瓶速度选择输出
);

    // ========================================
    // 时钟分频器
    // ========================================

    /*
     * 生成4个不同频率的时钟信号
     * T[0]: 输入处理时钟
     * T[1]: 状态机和指针控制时钟
     * T[2]: 数据处理时钟
     * T[3]: 输出生成时钟
     */
    logic T [0:3];
    timing #(
        .timing_count(4)
    ) timing_ins (
        .clock(clock),
        .reset_n(reset_n),
        .clock_out(T)
    );

    // ========================================
    // Part 0: 输入处理模块
    // ========================================

    /*
     * 输入信号处理
     * - 将电平信号转换为脉冲信号
     * - 防止按键抖动和重复触发
     * - 检测键盘、按钮和药片传感器的输入
     */
    logic [15:0] keyboard_down;             // 键盘按下脉冲信号
    logic [4:0] button_down;                // 按钮按下脉冲信号
    logic pill_pulse;                       // 药片检测脉冲信号

    input_receiver ir_ins (
        .clock(T[0]),
        .reset_n(reset_n),
        .keyboard(keyboard),
        .button(button),
        .pill(pill),
        .keyboard_down(keyboard_down),
        .button_down(button_down),
        .pill_pulse(pill_pulse)
    );

    // ========================================
    // Part 1: 状态机和指针控制模块
    // ========================================

    /*
     * 系统状态管理
     * - 控制系统在不同状态间的转换
     * - 响应用户操作和系统事件
     */
    state_t state;                          // 当前系统状态
    logic switch_signal;                    // 状态切换信号
    logic complete_signal;                  // 装瓶完成信号
    logic error_signal;                     // 错误信号 (在后面定义)

    assign switch_signal = button_down[3];  // 按钮3用于状态切换

    // ========================================
    // 静音功能控制逻辑
    // ========================================

    /*
     * 静音状态管理
     * - 使用键盘F键来切换静音状态
     * - F键位置：keyboard_down[3][0] (键盘布局中的F键)
     * - 提供持续的静音状态保持机制
     * - 避免与所有按钮功能冲突
     */
    logic mute_state;                       // 静音状态寄存器
    logic mute_toggle_signal;               // 静音切换信号
    logic last_mute_toggle;                 // 上一次静音切换信号状态

    // 静音切换信号：按下键盘F键
    // F键对应keyboard_down[3][0]，在重新映射中对应reinterpreted_keyboard['hf]
    assign mute_toggle_signal = keyboard_down[3][0];  // 直接使用F键位置

    /*
     * 静音状态切换逻辑
     * - 检测F键按下的上升沿来切换静音状态
     * - 防止重复触发
     * - 复位时清除静音状态
     */
    always_ff @(posedge T[1] or negedge reset_n) begin
        if (~reset_n) begin
            mute_state <= 0;                // 复位时取消静音
            last_mute_toggle <= 0;
        end else begin
            last_mute_toggle <= mute_toggle_signal;
            // 检测F键按下的上升沿
            if (mute_toggle_signal && ~last_mute_toggle) begin
                mute_state <= ~mute_state;  // 切换静音状态
            end
        end
    end

    state_machine sm_ins (
        .clock(T[1]),
        .reset_n(reset_n),
        .switch_signal(switch_signal),
        .error_signal(error_signal),
        .complete_signal(complete_signal),
        .state(state)
    );

    /*
     * 水平指针控制 (用于选择数字位置)
     * - 范围：0-3 (对应千位、百位、十位、个位)
     * - 按钮1：向右移动 (增加)
     * - 按钮0：向左移动 (减少)
     * - 静音功能使用F键，不会与按钮功能冲突
     */
    logic [1:0] h_ptr;                      // 水平指针 (数字位置选择)
    pointer_modifier #(.modulus(4)) hpm_ins (
        .clock(T[1]),
        .reset_n(reset_n),
        .add(button_down[1]),               // 按钮1：向右移动
        .sub(button_down[0]),               // 按钮0：向左移动
        .pointer(h_ptr)
    );

    /*
     * 垂直指针控制 (用于选择菜单项目)
     * - 范围：0-5 (包含速度设置页面)
     * - 按钮4：向下移动 (增加)
     * - 按钮2：向上移动 (减少)
     */
    logic [2:0] v_ptr;                      // 垂直指针 (菜单项目选择)
    pointer_modifier #(.modulus(6)) vpm_ins (
        .clock(T[1]),
        .reset_n(reset_n),
        .add(button_down[4]),               // 按钮4：向下移动
        .sub(button_down[2]),               // 按钮2：向上移动
        .pointer(v_ptr)
    );

    // ========================================
    // Part 2: 数据处理模块
    // ========================================

    /*
     * 数据管理和用户输入处理
     * - 管理药瓶数量和每瓶药片数量的设置
     * - 处理键盘输入和速度控制
     * - 生成警告信号
     */
    int jar_cnt [0:3];                      // 药瓶数量的四位数字存储
    int one_cnt [0:3];                      // 每瓶药片数量的四位数字存储
    int jar_number;                         // 药瓶总数 (由四位数字组合而成)
    int one_number;                         // 每瓶药片数 (由四位数字组合而成)
    logic keyboard_warning;                 // 键盘输入警告信号
    speed_t speed_setting;                  // 装瓶速度设置

    // 将四位数字组合成完整的数值
    assign jar_number = ((jar_cnt[3] * 10 + jar_cnt[2]) * 10 + jar_cnt[1]) * 10 + jar_cnt[0];
    assign one_number = ((one_cnt[3] * 10 + one_cnt[2]) * 10 + one_cnt[1]) * 10 + one_cnt[0];

    data_modifier dm_ins (
        .clock(T[2]),
        .reset_n(reset_n),
        .state(state),
        .h_ptr(h_ptr),
        .v_ptr(v_ptr),
        .jar_cnt(jar_cnt),
        .one_cnt(one_cnt),
        .keyboard_down(keyboard_down),
        // 速度控制按钮：仅在速度设置页面(v_ptr==2)且处于设置状态时有效
        .speed_up_btn(button_down[1] && (v_ptr == 2) && (state == setting_state)),
        .speed_down_btn(button_down[0] && (v_ptr == 2) && (state == setting_state)),
        .speed_setting(speed_setting),
        .keyboard_warning(keyboard_warning)
    );

    /*
     * 装瓶过程控制逻辑
     * - 管理装瓶过程中的计数和时序
     * - 检测错误情况并生成相应信号
     * - 控制装瓶完成的判断
     */
    int now_count;                          // 当前瓶中药片计数
    int now_jar_count;                      // 当前已装瓶数量
    int total_pill;                         // 总药片计数
    int tick;                               // 装瓶间隔时序计数器
    logic funnel_error;                     // 漏斗错误标志

    // 装瓶间隔时间参数 (2秒)
    localparam tick_limit = `frequency / 4 * 2;

    /*
     * 装瓶控制状态机
     * - 在工作状态和暂停状态下进行药片计数和装瓶控制
     * - 检测药片数量错误和时序错误
     * - 管理装瓶完成信号
     */
    always_ff @(posedge T[2] or negedge reset_n) begin
        if (~ reset_n) begin
            // 复位时初始化所有计数器和标志
            now_count <= 0;
            now_jar_count <= 0;
            total_pill <= 0;
            tick <= tick_limit - 1;
            funnel_error <= 0;
            complete_signal <= 0;
        end
        else if (state == working_state || state == pause_state) begin
            // 工作状态或暂停状态下的装瓶逻辑
            if (pill_pulse) begin
                // 检测到药片通过传感器
                if (state != working_state || now_count == one_number) begin
                    // 错误情况：在非工作状态下检测到药片，或当前瓶已满
                    funnel_error = 1;
                end else begin
                    // 正常情况：增加药片计数
                    total_pill = total_pill + 1;
                    now_count = now_count + 1;
                    if (now_count == one_number) begin
                        // 当前瓶装满，启动装瓶间隔计时器
                        tick = tick_limit - 1;
                    end
                end
            end else if (tick) begin
                // 装瓶间隔计时器倒计时
                if (tick == 1) begin
                    // 计时结束，准备装下一瓶
                    tick = 0;
                    now_count = 0;
                    if (now_jar_count == jar_number) begin
                        // 所有瓶子装完，设置完成信号
                        complete_signal = 1;
                    end
                    now_jar_count = now_jar_count + 1;
                end else begin
                    tick = tick - 1;
                end
            end
        end else if (state == setting_state) begin
            // 设置状态下重置所有计数器
            now_count <= 0;
            now_jar_count <= 0;
            total_pill <= 0;
            tick <= tick_limit - 1;
            funnel_error <= 0;
            complete_signal <= 0;
        end
    end

    // 控制信号生成
    assign funnel_disable = (state != working_state) || tick;  // 漏斗禁用：非工作状态或装瓶间隔期间
    assign error_signal = funnel_error || (! jar_number) || (! one_number);  // 错误信号：漏斗错误或设置为0
    assign speed_select = speed_setting;                       // 速度选择输出

    // ========================================
    // Part 3: 输出生成模块
    // ========================================

    /*
     * 显示数据生成
     * - 根据当前状态和指针位置生成显示内容
     * - 管理不同页面的显示切换
     * - 提供用户界面反馈
     */
    display_data_generator ddg_ins (
        .clock(T[3]),
        .reset_n(reset_n),
        .state(state),
        .h_ptr(h_ptr),
        .v_ptr(v_ptr),
        .jar_number(jar_number),
        .one_number(one_number),
        .now_count(now_count),
        .now_jar_count(now_jar_count),
        .total_pill(total_pill),
        .speed_setting(speed_setting),
        .display_code(display_code),
        .display_dp(display_dp)
    );

    /*
     * 音频输出生成
     * - 根据系统状态生成相应的音频信号
     * - 提供音乐、错误音和警告音
     */
    logic music;                            // 音乐播放信号
    logic error;                            // 错误音播放信号
    logic warning;                          // 警告音播放信号

    assign music = (state == final_state);     // 装瓶完成时播放音乐
    assign error = (state == error_state);     // 错误状态时播放错误音
    assign warning = keyboard_warning;         // 键盘输入错误时播放警告音

    audio_output_generator aog_ins (
        .clock(T[3]),
        .reset_n(reset_n),
        .no_response(mute_state),           // 使用静音状态寄存器控制静音
        .music(music),
        .error(error),
        .warning(warning),
        .frequency_select(frequency_select)
    );

endmodule
