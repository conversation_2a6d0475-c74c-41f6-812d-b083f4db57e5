# 项目名称修改指南：从 basic2Pill 到 Pill_device

## 概述

本指南详细说明如何将Vivado项目名称从 `basic2Pill` 修改为 `Pill_device`。我们提供了两种方法：TCL脚本重建（推荐）和手动修改。

## 方法一：TCL脚本重建（推荐）

这是最安全和最彻底的方法，通过重新创建项目来实现名称修改。

### 步骤1：备份当前项目
```bash
# 在项目根目录下创建备份
cp -r Pill_device/prj Pill_device/prj_backup_$(date +%Y%m%d)
```

### 步骤2：修改TCL脚本
已经完成修改，`Pill_device/scripts/recreate_project.tcl` 中的项目名称已更新为：
```tcl
set proj_name "Pill_device"
```

### 步骤3：删除旧项目文件
在Vivado TCL Console中执行：
```tcl
cd <你的项目路径>/Pill_device/scripts/
file delete -force ../prj/
```

### 步骤4：重建项目
在Vivado TCL Console中执行：
```tcl
source ./recreate_project.tcl
```

### 步骤5：验证项目
- 检查项目名称是否为 `Pill_device`
- 验证所有源文件是否正确加载
- 确认约束文件是否正确关联

## 方法二：手动修改（高级用户）

如果您希望保留现有的项目历史和设置，可以手动修改项目文件。

### 步骤1：关闭Vivado项目

### 步骤2：重命名项目文件
```bash
cd Pill_device/prj/
mv basic2Pill.xpr Pill_device.xpr
mv basic2Pill.cache Pill_device.cache
mv basic2Pill.hw Pill_device.hw
mv basic2Pill.ip_user_files Pill_device.ip_user_files
mv basic2Pill.runs Pill_device.runs
mv basic2Pill.sim Pill_device.sim
mv basic2Pill.srcs Pill_device.srcs
```

### 步骤3：修改项目文件内容
编辑 `Pill_device.xpr` 文件，将所有 `basic2Pill` 替换为 `Pill_device`：

```xml
<!-- 修改前 -->
<Project Version="7" Minor="44" Path="basic2Pill.xpr">
  <DefaultLaunch Dir="$PRUNDIR"/>
  <Configuration>
    <Option Name="Id" Val="basic2Pill"/>
    <Option Name="Part" Val="xc7a100tfgg484-1"/>
    <Option Name="CompiledLibDir" Val="$PCACHEDIR/compile_simlib"/>
    <Option Name="CompiledLibDirXSim" Val=""/>
    <Option Name="CompiledLibDirModelSim" Val="$PCACHEDIR/compile_simlib/modelsim"/>
    <Option Name="CompiledLibDirQuesta" Val="$PCACHEDIR/compile_simlib/questa"/>
    <Option Name="CompiledLibDirIES" Val="$PCACHEDIR/compile_simlib/ies"/>
    <Option Name="CompiledLibDirXcelium" Val="$PCACHEDIR/compile_simlib/xcelium"/>
    <Option Name="CompiledLibDirVCS" Val="$PCACHEDIR/compile_simlib/vcs"/>
    <Option Name="CompiledLibDirRiviera" Val="$PCACHEDIR/compile_simlib/riviera"/>
    <Option Name="CompiledLibDirActivehdl" Val="$PCACHEDIR/compile_simlib/activehdl"/>
    <Option Name="BoardPart" Val=""/>
    <Option Name="ActiveSimSet" Val="sim_1"/>
    <Option Name="DefaultLib" Val="xil_defaultlib"/>
    <Option Name="ProjectType" Val="Default"/>
    <Option Name="IPOutputRepo" Val="$PCACHEDIR/ip"/>
    <Option Name="IPCachePermission" Val="read"/>
    <Option Name="IPCachePermission" Val="write"/>
    <Option Name="EnableCoreContainer" Val="FALSE"/>
    <Option Name="CreateRefXciForCoreContainers" Val="FALSE"/>
    <Option Name="IPUserFilesDir" Val="$PIPUSERFILESDIR"/>
    <Option Name="IPStaticSourceDir" Val="$PIPUSERFILESDIR/ipstatic"/>
    <Option Name="EnableBDX" Val="FALSE"/>
    <Option Name="DSAVendor" Val="xilinx"/>
    <Option Name="DSABoardId" Val=""/>
    <Option Name="DSANumComputeUnits" Val="60"/>
    <Option Name="WTXSimLaunchSim" Val="0"/>
    <Option Name="WTModelSimLaunchSim" Val="0"/>
    <Option Name="WTQuestaLaunchSim" Val="0"/>
    <Option Name="WTIesLaunchSim" Val="0"/>
    <Option Name="WTVcsLaunchSim" Val="0"/>
    <Option Name="WTRivieraLaunchSim" Val="0"/>
    <Option Name="WTActivehdlLaunchSim" Val="0"/>
    <Option Name="WTXSimExitSim" Val="0"/>
    <Option Name="WTModelSimExitSim" Val="0"/>
    <Option Name="WTQuestaExitSim" Val="0"/>
    <Option Name="WTIesExitSim" Val="0"/>
    <Option Name="WTVcsExitSim" Val="0"/>
    <Option Name="WTRivieraExitSim" Val="0"/>
    <Option Name="WTActivehdlExitSim" Val="0"/>
    <Option Name="GenerateIPUpgradeLog" Val="TRUE"/>
    <Option Name="XSimRadix" Val="hex"/>
    <Option Name="XSimTimeUnit" Val="ns"/>
    <Option Name="XSimArrayDisplayLimit" Val="1024"/>
    <Option Name="XSimTraceLimit" Val="65536"/>
    <Option Name="SimTypes" Val="rtl"/>
    <Option Name="SimTypes" Val="bfm"/>
    <Option Name="SimTypes" Val="tlm"/>
    <Option Name="SimTypes" Val="tlm_dpi"/>
    <Option Name="MEMEnableMemoryMapGeneration" Val="TRUE"/>
  </Configuration>

<!-- 修改后 -->
<Project Version="7" Minor="44" Path="Pill_device.xpr">
  <DefaultLaunch Dir="$PRUNDIR"/>
  <Configuration>
    <Option Name="Id" Val="Pill_device"/>
    <!-- 其他配置保持不变 -->
  </Configuration>
```

### 步骤4：修改其他相关文件
检查并修改以下文件中的项目名称引用：
- `Pill_device/prj/vivado.jou`
- `Pill_device/prj/vivado.log`
- 任何包含项目名称的配置文件

## 验证修改结果

### 1. 打开项目
在Vivado中打开修改后的项目：
```tcl
open_project <项目路径>/Pill_device/prj/Pill_device.xpr
```

### 2. 检查项目属性
在Vivado中检查：
- Project Manager → Settings → General → Project name
- 应该显示为 "Pill_device"

### 3. 验证功能
- 确认所有源文件正确加载
- 运行综合检查是否有错误
- 验证约束文件是否正确应用

## 常见问题和解决方案

### 问题1：项目无法打开
**原因**：文件路径或名称不匹配
**解决方案**：
1. 检查所有文件名是否正确修改
2. 验证.xpr文件中的路径引用
3. 重新使用TCL脚本创建项目

### 问题2：源文件丢失
**原因**：文件路径在项目文件中未正确更新
**解决方案**：
1. 在Project Manager中重新添加源文件
2. 检查相对路径是否正确
3. 使用TCL脚本重建项目

### 问题3：约束文件无效
**原因**：约束文件路径未更新
**解决方案**：
1. 重新添加约束文件
2. 检查约束文件中的模块名称引用
3. 验证引脚分配是否正确

### 问题4：IP核无法识别
**原因**：IP核缓存路径包含旧项目名称
**解决方案**：
1. 重新生成IP核
2. 清除IP缓存：Tools → Settings → IP → Repository
3. 使用TCL脚本重建项目

## 推荐的最佳实践

### 1. 使用版本控制
在修改项目名称前：
```bash
git add .
git commit -m "Backup before renaming project from basic2Pill to Pill_device"
```

### 2. 创建备份
```bash
tar -czf basic2Pill_backup_$(date +%Y%m%d).tar.gz Pill_device/prj/
```

### 3. 分步验证
每完成一个步骤后，都要验证项目是否能正常打开和工作。

### 4. 文档更新
修改项目名称后，记得更新：
- README.md文件
- 项目文档
- 注释中的项目引用

## 总结

推荐使用**方法一（TCL脚本重建）**，因为：
1. 更安全，不会遗留旧的配置
2. 确保所有引用都正确更新
3. 生成的项目更干净
4. 避免手动修改可能引入的错误

如果您已经有重要的项目设置需要保留，可以考虑方法二，但需要更加仔细地检查每个修改步骤。
