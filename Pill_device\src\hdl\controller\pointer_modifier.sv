/*
 * 指针修改器模块
 *
 * 功能描述：
 * - 实现循环计数器功能，用于菜单导航和数据选择
 * - 支持增加和减少操作，具有模数限制
 * - 提供环形计数：到达最大值后回到0，到达0后回到最大值
 *
 * 参数说明：
 * - modulus: 计数器的模数，决定计数范围 [0, modulus-1]
 *
 * 应用场景：
 * - 水平指针：用于选择数字位置 (modulus = 4)
 * - 垂直指针：用于选择菜单项目 (modulus = 6)
 *
 * 操作逻辑：
 * - add信号：指针向前移动（增加）
 * - sub信号：指针向后移动（减少）
 * - 同时按下add和sub：指针保持不变
 *
 * 作者：数字逻辑课程设计小组
 * 日期：2024-2025学年
 */

`include "config.svh"

module pointer_modifier #(
    parameter modulus                   // 计数器模数参数
) (
    input logic clock,                  // 系统时钟
    input logic reset_n,                // 复位信号 (低电平有效)
    input logic add,                    // 增加信号 (指针向前移动)
    input logic sub,                    // 减少信号 (指针向后移动)
    output logic [$clog2(modulus)-1:0] pointer  // 指针输出 (范围: 0 到 modulus-1)
);
    // ========================================
    // 指针控制逻辑
    // ========================================

    /*
     * 循环计数器实现
     * - 时钟上升沿触发更新
     * - 异步复位到0
     * - 根据add和sub信号控制指针移动
     */
    always @(posedge clock or negedge reset_n) begin
        if (~reset_n) begin
            // 异步复位：指针回到起始位置
            pointer <= 'b0;
        end else begin
            if (add && sub) begin
                // 同时按下增加和减少按钮：指针保持不变
                pointer <= pointer;
            end else if (add) begin
                // 增加操作：指针向前移动
                if (pointer == modulus - 1) begin
                    // 到达最大值，环形回到0
                    pointer <= 0;
                end else begin
                    // 正常递增
                    pointer <= pointer + 1;
                end
            end else if (sub) begin
                // 减少操作：指针向后移动
                if (pointer == 0) begin
                    // 到达最小值，环形回到最大值
                    pointer <= modulus - 1;
                end else begin
                    // 正常递减
                    pointer <= pointer - 1;
                end
            end else begin
                // 无操作：指针保持当前值
                pointer <= pointer;
            end
        end
    end
endmodule
