# 简化版项目创建脚本 - 解决 rtd 文件错误
# 作者：数字逻辑课程设计小组
# 日期：2024-2025学年

# 设置项目参数
set origin_dir "../"
set proj_name "Pill_device"
set orig_proj_dir "[file normalize "$origin_dir/prj"]"

puts "Creating project: $proj_name"
puts "Project directory: $orig_proj_dir"

# 创建项目
create_project ${proj_name} ${orig_proj_dir} -part xc7a100tfgg484-1 -force

# 获取项目目录
set proj_dir [get_property directory [current_project]]
puts "Project created at: $proj_dir"

# 设置基本项目属性
set obj [current_project]
set_property -name "default_lib" -value "xil_defaultlib" -objects $obj
set_property -name "part" -value "xc7a100tfgg484-1" -objects $obj
set_property -name "target_language" -value "Verilog" -objects $obj

# 创建源文件集合
if {[string equal [get_filesets -quiet sources_1] ""]} {
  create_fileset -srcset sources_1
}

# 定义源文件列表
set source_files [list \
  "${origin_dir}/src/hdl/config.svh" \
  "${origin_dir}/src/hdl/top_module.sv" \
  "${origin_dir}/src/hdl/controller/controller.sv" \
  "${origin_dir}/src/hdl/controller/state_machine.sv" \
  "${origin_dir}/src/hdl/controller/input_receiver.sv" \
  "${origin_dir}/src/hdl/controller/data_modifier.sv" \
  "${origin_dir}/src/hdl/controller/pointer_modifier.sv" \
  "${origin_dir}/src/hdl/controller/display_data_generator.sv" \
  "${origin_dir}/src/hdl/controller/audio_output_generator.sv" \
  "${origin_dir}/../module/buttons.sv" \
  "${origin_dir}/../module/keyboards.sv" \
  "${origin_dir}/../module/digital_tube.sv" \
  "${origin_dir}/../module/audio_generator.sv" \
  "${origin_dir}/../module/pill_simulator.sv" \
  "${origin_dir}/../util/combinational/segment_decoder.sv" \
  "${origin_dir}/../util/sequential/counter.sv" \
  "${origin_dir}/../util/sequential/debouncer.sv" \
  "${origin_dir}/../util/sequential/divider.sv" \
  "${origin_dir}/../util/sequential/synchronizer.sv" \
  "${origin_dir}/../util/sequential/timing.sv" \
  "${origin_dir}/../util/sequential/v_divider.sv" \
]

# 添加源文件
set obj [get_filesets sources_1]
set files_added 0
set files_missing 0

foreach src_file $source_files {
  set normalized_file [file normalize $src_file]
  puts "Checking file: $normalized_file"
  
  if {[file exists $normalized_file]} {
    puts "  Adding file: [file tail $normalized_file]"
    add_files -norecurse -fileset $obj $normalized_file
    incr files_added
  } else {
    puts "  WARNING: File not found: $normalized_file"
    incr files_missing
  }
}

puts "Files added: $files_added"
puts "Files missing: $files_missing"

# 设置config.svh为Verilog Header类型
set config_file [get_files -of_objects [get_filesets sources_1] "*config.svh"]
if {[llength $config_file] > 0} {
  set_property file_type {Verilog Header} $config_file
  puts "Set config.svh as Verilog Header"
}

# 设置顶层模块
set_property -name "top" -value "top_module" -objects $obj

# 创建约束文件集合
if {[string equal [get_filesets -quiet constrs_1] ""]} {
  create_fileset -constrset constrs_1
}

# 添加约束文件（如果存在）
set constraint_file "${origin_dir}/src/xdc/constraints.xdc"
set normalized_constraint [file normalize $constraint_file]

if {[file exists $normalized_constraint]} {
  set obj [get_filesets constrs_1]
  add_files -norecurse -fileset $obj $normalized_constraint
  puts "Added constraint file: $normalized_constraint"
} else {
  puts "No constraint file found at: $normalized_constraint"
  puts "You can add constraint files later if needed."
}

# 创建仿真文件集合
if {[string equal [get_filesets -quiet sim_1] ""]} {
  create_fileset -simset sim_1
}

# 设置仿真属性
set obj [get_filesets sim_1]
set_property -name "top" -value "top_module" -objects $obj
set_property -name "top_lib" -value "xil_defaultlib" -objects $obj

puts "Project creation completed successfully!"
puts "Project name: $proj_name"
puts "You can now open the project in Vivado GUI or continue with synthesis."

# 可选：自动打开项目
# start_gui
